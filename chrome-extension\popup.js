// Popup script для отображения найденных токенов

document.addEventListener('DOMContentLoaded', function() {
    const statusEl = document.getElementById('status');
    const accessTokenEl = document.getElementById('accessToken');
    const sessionKeyEl = document.getElementById('sessionKey');
    const userIdEl = document.getElementById('userId');
    const appIdEl = document.getElementById('appId');
    const lastUpdatedEl = document.getElementById('lastUpdated');
    const powershellCodeEl = document.getElementById('powershellCode');
    
    const refreshBtn = document.getElementById('refreshBtn');
    const copyBtn = document.getElementById('copyBtn');
    const clearBtn = document.getElementById('clearBtn');
    
    let currentTokens = {};
    
    // Функция для обновления UI
    function updateUI(tokens) {
        currentTokens = tokens;
        
        // Обновляем токены
        updateTokenDisplay(accessTokenEl, tokens.accessToken);
        updateTokenDisplay(sessionKeyEl, tokens.sessionKey);
        updateTokenDisplay(userIdEl, tokens.userId);
        updateTokenDisplay(appIdEl, tokens.appId);
        
        // Обновляем статус
        const hasTokens = tokens.accessToken && tokens.sessionKey;
        if (hasTokens) {
            statusEl.className = 'status success';
            statusEl.textContent = '✅ Токены найдены!';
        } else if (tokens.accessToken || tokens.sessionKey) {
            statusEl.className = 'status warning';
            statusEl.textContent = '⚠️ Найдены не все токены';
        } else {
            statusEl.className = 'status error';
            statusEl.textContent = '❌ Токены не найдены';
        }
        
        // Обновляем время
        if (tokens.lastUpdated) {
            const date = new Date(tokens.lastUpdated);
            lastUpdatedEl.textContent = `Обновлено: ${date.toLocaleString('ru-RU')}`;
        }
        
        // Обновляем PowerShell код
        updatePowerShellCode(tokens);
    }
    
    function updateTokenDisplay(element, value) {
        if (value) {
            element.textContent = value;
            element.className = 'token-value';
        } else {
            element.textContent = 'Не найден';
            element.className = 'token-value empty';
        }
    }
    
    function updatePowerShellCode(tokens) {
        if (tokens.accessToken && tokens.sessionKey) {
            const userId = tokens.userId || '6484795'; // fallback к известному ID
            const code = `# Токены для G2G Sendbird API
$AccessToken = "${tokens.accessToken}"
$SessionKey = "${tokens.sessionKey}"
$UserId = "${userId}"

# Запуск скрипта проверки сообщений
.\\get_unread_messages.ps1 -AccessToken $AccessToken -SessionKey $SessionKey -UserId $UserId`;
            
            powershellCodeEl.textContent = code;
        } else {
            powershellCodeEl.textContent = 'Токены не найдены. Откройте чат на G2G и попробуйте снова.';
        }
    }
    
    // Загружаем токены при открытии popup
    function loadTokens() {
        chrome.runtime.sendMessage({ type: 'GET_TOKENS' }, (response) => {
            if (response) {
                updateUI(response);
            }
        });
    }
    
    // Обработчики кнопок
    refreshBtn.addEventListener('click', function() {
        statusEl.className = 'status warning';
        statusEl.textContent = 'Обновление...';
        
        // Запрашиваем обновление от content script
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            if (tabs[0] && tabs[0].url.includes('g2g.com')) {
                chrome.tabs.sendMessage(tabs[0].id, { type: 'EXTRACT_TOKENS' }, function(response) {
                    setTimeout(loadTokens, 1000); // Даем время на обработку
                });
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ Откройте вкладку G2G';
            }
        });
    });
    
    copyBtn.addEventListener('click', function() {
        const code = powershellCodeEl.textContent;
        navigator.clipboard.writeText(code).then(function() {
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Скопировано!';
            copyBtn.style.backgroundColor = '#28a745';
            
            setTimeout(function() {
                copyBtn.textContent = originalText;
                copyBtn.style.backgroundColor = '';
            }, 2000);
        }).catch(function(err) {
            console.error('Ошибка копирования:', err);
            // Fallback для старых браузеров
            const textArea = document.createElement('textarea');
            textArea.value = code;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            copyBtn.textContent = 'Скопировано!';
            setTimeout(function() {
                copyBtn.textContent = 'Копировать PowerShell';
            }, 2000);
        });
    });
    
    clearBtn.addEventListener('click', function() {
        chrome.runtime.sendMessage({ type: 'CLEAR_TOKENS' }, function(response) {
            if (response && response.success) {
                updateUI({});
                statusEl.className = 'status warning';
                statusEl.textContent = 'Токены очищены';
            }
        });
    });
    
    // Слушаем обновления токенов
    chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
        if (message.type === 'TOKENS_UPDATED') {
            updateUI(message.tokens);
        }
    });
    
    // Загружаем токены при запуске
    loadTokens();
});
