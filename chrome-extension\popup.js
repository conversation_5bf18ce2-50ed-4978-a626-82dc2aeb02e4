// Popup script для отображения найденных токенов

document.addEventListener('DOMContentLoaded', function() {
    const statusEl = document.getElementById('status');
    const accessTokenEl = document.getElementById('accessToken');
    const sessionKeyEl = document.getElementById('sessionKey');
    const userIdEl = document.getElementById('userId');
    const appIdEl = document.getElementById('appId');
    const lastUpdatedEl = document.getElementById('lastUpdated');
    const powershellCodeEl = document.getElementById('powershellCode');

    const refreshBtn = document.getElementById('refreshBtn');
    const copyBtn = document.getElementById('copyBtn');
    const clearBtn = document.getElementById('clearBtn');

    // Элементы мониторинга
    const toggleMonitoringBtn = document.getElementById('toggleMonitoringBtn');
    const checkNowBtn = document.getElementById('checkNowBtn');
    const testNotificationBtn = document.getElementById('testNotificationBtn');
    const intervalSelect = document.getElementById('intervalSelect');
    const monitoringStatusEl = document.getElementById('monitoringStatus');

    let currentTokens = {};
    let monitoringSettings = {};
    
    // Функция для обновления UI
    function updateUI(tokens) {
        currentTokens = tokens;
        
        // Обновляем токены
        updateTokenDisplay(accessTokenEl, tokens.accessToken);
        updateTokenDisplay(sessionKeyEl, tokens.sessionKey);
        updateTokenDisplay(userIdEl, tokens.userId);
        updateTokenDisplay(appIdEl, tokens.appId);
        
        // Обновляем статус
        const hasTokens = tokens.accessToken && tokens.sessionKey;
        if (hasTokens) {
            statusEl.className = 'status success';
            statusEl.textContent = '✅ Токены найдены!';
        } else if (tokens.accessToken || tokens.sessionKey) {
            statusEl.className = 'status warning';
            statusEl.textContent = '⚠️ Найдены не все токены';
        } else {
            statusEl.className = 'status error';
            statusEl.textContent = '❌ Токены не найдены';
        }
        
        // Обновляем время
        if (tokens.lastUpdated) {
            const date = new Date(tokens.lastUpdated);
            lastUpdatedEl.textContent = `Обновлено: ${date.toLocaleString('ru-RU')}`;
        }
        
        // Обновляем PowerShell код
        updatePowerShellCode(tokens);
    }
    
    function updateTokenDisplay(element, value) {
        if (value) {
            element.textContent = value;
            element.className = 'token-value';
        } else {
            element.textContent = 'Не найден';
            element.className = 'token-value empty';
        }
    }
    
    function updatePowerShellCode(tokens) {
        if (tokens.accessToken && tokens.sessionKey) {
            const userId = tokens.userId || '6484795'; // fallback к известному ID
            const code = `# Токены для G2G Sendbird API
$AccessToken = "${tokens.accessToken}"
$SessionKey = "${tokens.sessionKey}"
$UserId = "${userId}"

# Запуск скрипта проверки сообщений
.\\get_unread_messages.ps1 -AccessToken $AccessToken -SessionKey $SessionKey -UserId $UserId`;
            
            powershellCodeEl.textContent = code;
        } else {
            powershellCodeEl.textContent = 'Токены не найдены. Откройте чат на G2G и попробуйте снова.';
        }
    }
    
    // Функция для обновления статуса мониторинга
    function updateMonitoringUI(settings) {
        monitoringSettings = settings;

        if (settings.enabled) {
            toggleMonitoringBtn.textContent = 'Остановить мониторинг';
            toggleMonitoringBtn.className = 'btn-secondary';
            monitoringStatusEl.textContent = `Активен (проверок: ${settings.successfulChecks}/${settings.totalChecks})`;
            monitoringStatusEl.className = 'monitoring-status active';

            if (settings.lastCheckTime) {
                const lastCheck = new Date(settings.lastCheckTime);
                monitoringStatusEl.textContent += ` | Последняя: ${lastCheck.toLocaleTimeString('ru-RU')}`;
            }
        } else {
            toggleMonitoringBtn.textContent = 'Запустить мониторинг';
            toggleMonitoringBtn.className = 'btn-primary';
            monitoringStatusEl.textContent = 'Отключен';
            monitoringStatusEl.className = 'monitoring-status inactive';
        }

        intervalSelect.value = settings.interval || 1;
    }

    // Загружаем токены и статус мониторинга при открытии popup
    function loadData() {
        chrome.runtime.sendMessage({ type: 'GET_TOKENS' }, (response) => {
            if (response) {
                updateUI(response);
            }
        });

        chrome.runtime.sendMessage({ type: 'GET_MONITORING_STATUS' }, (response) => {
            if (response) {
                updateMonitoringUI(response);
            }
        });
    }
    
    // Обработчики кнопок
    refreshBtn.addEventListener('click', function() {
        statusEl.className = 'status warning';
        statusEl.textContent = 'Обновление...';
        
        // Запрашиваем обновление от content script
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            if (tabs[0] && tabs[0].url.includes('g2g.com')) {
                chrome.tabs.sendMessage(tabs[0].id, { type: 'EXTRACT_TOKENS' }, function(response) {
                    setTimeout(loadTokens, 1000); // Даем время на обработку
                });
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ Откройте вкладку G2G';
            }
        });
    });
    
    copyBtn.addEventListener('click', function() {
        const code = powershellCodeEl.textContent;
        navigator.clipboard.writeText(code).then(function() {
            const originalText = copyBtn.textContent;
            copyBtn.textContent = 'Скопировано!';
            copyBtn.style.backgroundColor = '#28a745';
            
            setTimeout(function() {
                copyBtn.textContent = originalText;
                copyBtn.style.backgroundColor = '';
            }, 2000);
        }).catch(function(err) {
            console.error('Ошибка копирования:', err);
            // Fallback для старых браузеров
            const textArea = document.createElement('textarea');
            textArea.value = code;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            copyBtn.textContent = 'Скопировано!';
            setTimeout(function() {
                copyBtn.textContent = 'Копировать PowerShell';
            }, 2000);
        });
    });
    
    clearBtn.addEventListener('click', function() {
        chrome.runtime.sendMessage({ type: 'CLEAR_TOKENS' }, function(response) {
            if (response && response.success) {
                updateUI({});
                updateMonitoringUI({ enabled: false, interval: 1, totalChecks: 0, successfulChecks: 0 });
                statusEl.className = 'status warning';
                statusEl.textContent = 'Токены очищены';
            }
        });
    });

    // Обработчики мониторинга
    toggleMonitoringBtn.addEventListener('click', function() {
        if (monitoringSettings.enabled) {
            // Останавливаем мониторинг
            chrome.runtime.sendMessage({ type: 'STOP_MONITORING' }, function(response) {
                if (response && response.success) {
                    updateMonitoringUI({ ...monitoringSettings, enabled: false });
                }
            });
        } else {
            // Запускаем мониторинг
            const interval = parseInt(intervalSelect.value);
            chrome.runtime.sendMessage({
                type: 'START_MONITORING',
                interval: interval
            }, function(response) {
                if (response && response.success) {
                    updateMonitoringUI({ ...monitoringSettings, enabled: true, interval: interval });
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = response ? response.message : 'Ошибка запуска мониторинга';
                }
            });
        }
    });

    checkNowBtn.addEventListener('click', function() {
        checkNowBtn.textContent = 'Проверяю...';
        checkNowBtn.disabled = true;

        chrome.runtime.sendMessage({ type: 'CHECK_MESSAGES_NOW' }, function(response) {
            checkNowBtn.textContent = 'Проверить сейчас';
            checkNowBtn.disabled = false;

            if (response && response.success) {
                statusEl.className = 'status success';
                statusEl.textContent = `✅ Непрочитанных сообщений: ${response.unreadCount}`;
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ Ошибка: ${response ? response.error : 'Неизвестная ошибка'}`;
            }

            // Обновляем статус мониторинга
            chrome.runtime.sendMessage({ type: 'GET_MONITORING_STATUS' }, (response) => {
                if (response) {
                    updateMonitoringUI(response);
                }
            });
        });
    });

    // Тест уведомлений
    testNotificationBtn.addEventListener('click', function() {
        testNotificationBtn.textContent = 'Отправляю...';
        testNotificationBtn.disabled = true;

        // Отправляем сообщение background script для показа тестового уведомления
        chrome.runtime.sendMessage({
            type: 'TEST_NOTIFICATION',
            count: 5 // Тестовое количество сообщений
        }, function(response) {
            testNotificationBtn.textContent = 'Тест уведомления';
            testNotificationBtn.disabled = false;

            if (response && response.success) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ Тестовое уведомление отправлено';
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ Ошибка теста: ${response ? response.error : 'Неизвестная ошибка'}`;
            }
        });
    });

    // Слушаем обновления токенов
    chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
        if (message.type === 'TOKENS_UPDATED') {
            updateUI(message.tokens);
        }
    });
    
    // Загружаем данные при запуске
    loadData();
});
