# 🧪 Инструкции по тестированию уведомлений

## 🔧 Исправления внесены:

1. **Подробная отладка** - теперь в консоли видно почему уведомление показывается или нет
2. **Настройка поведения** - можно выбрать режим уведомлений
3. **Сброс счетчика** - при запуске мониторинга первая проверка всегда покажет уведомление

## 🚀 Как протестировать:

### Шаг 1: Обновите расширение
```
1. chrome://extensions/
2. G2G Message Monitor → Переключатель OFF/ON
3. Или нажмите кнопку "Обновить" (⟳)
```

### Шаг 2: Проверьте настройки
```
1. Откройте popup расширения
2. Убедитесь что токены найдены
3. Поставьте галочку "Уведомления при каждой проверке"
4. Выберите интервал 1-2 минуты для быстрого теста
```

### Шаг 3: Запустите мониторинг
```
1. Нажмите "Запустить мониторинг"
2. Первая проверка через 6 секунд должна показать уведомление
3. Следующие проверки каждые 1-2 минуты
```

### Шаг 4: Проверьте логи
```
1. chrome://extensions/
2. G2G Message Monitor → "Подробности"
3. "Просмотр в инспекторе фоновых страниц"
4. Смотрите консоль на сообщения:
   - "🚀 Запуск мониторинга..."
   - "🔔 Режим уведомлений: при каждой проверке"
   - "⏰ Автоматическая проверка сообщений по расписанию"
   - "🔍 Анализ условий уведомления"
   - "🔔 Показываем уведомление" или "🔕 Уведомление НЕ показываем"
```

## 🔍 Режимы уведомлений:

### Режим 1: "При каждой проверке" (рекомендуется для теста)
- ✅ Галочка включена
- Показывает уведомление при каждой проверке если есть сообщения
- Подходит если хотите знать о сообщениях постоянно

### Режим 2: "Только при изменении" (по умолчанию)
- ❌ Галочка выключена  
- Показывает уведомление только когда количество сообщений изменилось
- Подходит чтобы не спамить уведомлениями

## 🐛 Что смотреть в логах:

### ✅ Правильная работа:
```
🚀 Запуск мониторинга с интервалом 1 минут
🔔 Режим уведомлений: при каждой проверке
⏰ Автоматическая проверка сообщений по расписанию
🔍 Анализ условий уведомления: {unreadCount: 1, lastUnreadCount: null, ...}
🔔 Показываем уведомление: unreadCount=1, lastCount=null, force=false, firstCheck=true
🔔 Попытка показать уведомление: G2G - Новые сообщения! - У вас 1 непрочитанных сообщений
✅ Уведомление создано успешно: g2g-notification-1234567890
```

### ❌ Проблемы:
```
🔕 Уведомление НЕ показываем: unreadCount=1, lastCount=1, force=false
// Значит режим "только при изменении" и количество не изменилось

Ошибка создания уведомления: [объект ошибки]
// Проблема с разрешениями или системными настройками
```

## 🎯 Быстрый тест:

1. **Включите режим "При каждой проверке"**
2. **Установите интервал 1 минута**
3. **Запустите мониторинг**
4. **Через 6 секунд должно появиться уведомление**
5. **Каждую минуту должны появляться новые уведомления**

## 🔧 Если не работает:

1. **Проверьте консоль** - там должны быть подробные логи
2. **Попробуйте "Тест уведомления"** - если работает, проблема в логике
3. **Проверьте разрешения** - chrome://settings/content/notifications
4. **Перезапустите Chrome** полностью

## 📊 Badge индикатор:

Независимо от уведомлений, на иконке расширения должна появляться красная цифра с количеством сообщений. Это работает всегда, даже если push-уведомления заблокированы.
