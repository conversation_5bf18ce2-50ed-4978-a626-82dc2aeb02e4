<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 400px;
            padding: 15px;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
        .header {
            text-align: center;
            color: #1a73e8;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .token-section {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .token-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .token-value {
            font-family: monospace;
            font-size: 12px;
            background-color: #fff;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            word-break: break-all;
            max-height: 60px;
            overflow-y: auto;
        }
        .token-value.empty {
            color: #999;
            font-style: italic;
        }
        .status {
            text-align: center;
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        button {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #1a73e8;
            color: white;
        }
        .btn-primary:hover {
            background-color: #1557b0;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .last-updated {
            text-align: center;
            font-size: 11px;
            color: #666;
            margin-top: 10px;
        }
        .powershell-section {
            margin-top: 15px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .powershell-code {
            font-family: monospace;
            font-size: 11px;
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 8px;
            border-radius: 3px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .monitoring-section {
            margin: 15px 0;
            padding: 10px;
            border: 2px solid #1a73e8;
            border-radius: 5px;
            background-color: #f8f9ff;
        }
        .monitoring-controls {
            display: flex;
            gap: 5px;
            margin: 10px 0;
        }
        .monitoring-controls button {
            flex: 1;
            padding: 6px 8px;
            font-size: 11px;
        }
        .monitoring-settings {
            margin: 10px 0;
        }
        .monitoring-settings label {
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
        }
        .monitoring-settings select {
            padding: 4px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 11px;
        }
        .monitoring-status {
            font-size: 11px;
            padding: 5px;
            border-radius: 3px;
            text-align: center;
            font-weight: bold;
        }
        .monitoring-status.active {
            background-color: #d4edda;
            color: #155724;
        }
        .monitoring-status.inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="header">G2G Message Monitor</div>

    <div id="status" class="status warning">
        Поиск токенов...
    </div>

    <!-- Секция мониторинга -->
    <div class="monitoring-section">
        <div class="token-label">Автоматический мониторинг:</div>
        <div class="monitoring-controls">
            <button id="toggleMonitoringBtn" class="btn-primary">Запустить мониторинг</button>
            <button id="checkNowBtn" class="btn-secondary">Проверить сейчас</button>
        </div>
        <div class="monitoring-controls">
            <button id="testNotificationBtn" class="btn-success">Тест уведомления</button>
        </div>
        <div class="monitoring-settings">
            <label for="intervalSelect">Интервал проверки:</label>
            <select id="intervalSelect">
                <option value="1">1 минута</option>
                <option value="2">2 минуты</option>
                <option value="5">5 минут</option>
                <option value="10">10 минут</option>
                <option value="15">15 минут</option>
            </select>
        </div>
        <div id="monitoringStatus" class="monitoring-status">
            Мониторинг отключен
        </div>
    </div>
    
    <div class="token-section">
        <div class="token-label">Access Token:</div>
        <div id="accessToken" class="token-value empty">Не найден</div>
    </div>
    
    <div class="token-section">
        <div class="token-label">Session Key:</div>
        <div id="sessionKey" class="token-value empty">Не найден</div>
    </div>
    
    <div class="token-section">
        <div class="token-label">User ID:</div>
        <div id="userId" class="token-value empty">Не найден</div>
    </div>
    
    <div class="token-section">
        <div class="token-label">App ID:</div>
        <div id="appId" class="token-value empty">Не найден</div>
    </div>
    
    <div class="buttons">
        <button id="refreshBtn" class="btn-primary">Обновить</button>
        <button id="copyBtn" class="btn-success">Копировать PowerShell</button>
        <button id="clearBtn" class="btn-secondary">Очистить</button>
    </div>
    
    <div class="powershell-section">
        <div class="token-label">PowerShell команда:</div>
        <div id="powershellCode" class="powershell-code">Токены не найдены</div>
    </div>
    
    <div id="lastUpdated" class="last-updated">Никогда не обновлялось</div>
    
    <script src="popup.js"></script>
</body>
</html>
