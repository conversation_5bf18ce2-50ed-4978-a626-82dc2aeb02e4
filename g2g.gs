function setG2gCookies() {
  const ui = SpreadsheetApp.getUi();
  const response = ui.prompt(
    'Установка Cookies для G2G',
    'Вставьте полную строку cookies для g2g.com:',
    ui.ButtonSet.OK_CANCEL
  );

  if (response.getSelectedButton() == ui.Button.OK) {
    const cookies = response.getResponseText().trim();
    if (cookies) {
      PropertiesService.getScriptProperties().setProperty('g2g_cookies', cookies);
      // Попробуем извлечь CSRF токен из кук, если он там есть (на G2G он называется YII_CSRF_TOKEN)
      const csrfMatch = cookies.match(/YII_CSRF_TOKEN=([^;]+)/);
      if (csrfMatch && csrfMatch[1]) {
         PropertiesService.getScriptProperties().setProperty('g2g_csrf_token', csrfMatch[1]);
         ui.alert('Cookies и CSRF токен для G2G успешно сохранены!');
         Logger.log('G2G Cookies сохранены.');
         Logger.log('G2G CSRF Token сохранен: ' + csrfMatch[1]);
      } else {
         PropertiesService.getScriptProperties().deleteProperty('g2g_csrf_token'); // Удаляем старый если есть
         ui.alert('Cookies для G2G сохранены, но YII_CSRF_TOKEN не найден в строке cookies.');
         Logger.log('G2G Cookies сохранены, но YII_CSRF_TOKEN не найден.');
      }

    } else {
      ui.alert('Ошибка: строка cookies не может быть пустой.');
    }
  }
}

function showG2gCookies() {
  const props = PropertiesService.getScriptProperties();
  const cookies = props.getProperty('g2g_cookies') || 'Не установлены';
  const csrf = props.getProperty('g2g_csrf_token') || 'Не найден';
  const message = `Текущие данные для G2G:\n\nCookies: ${cookies}\n\nCSRF Token (YII_CSRF_TOKEN): ${csrf}`;
  SpreadsheetApp.getUi().alert(message);
}

function clearG2gCookies() {
  const props = PropertiesService.getScriptProperties();
  props.deleteProperty('g2g_cookies');
  props.deleteProperty('g2g_csrf_token');
  SpreadsheetApp.getActiveSpreadsheet().toast("Данные G2G (Cookies и CSRF) успешно очищены!", "Успех", 2);
  Logger.log('G2G Cookies и CSRF очищены.');
}

/**
 * Sends a request to update a specific field of a G2G listing.
 * @param {string} listingId The listing ID (pk).
 * @param {string} fieldName The name of the field to update (e.g., 'products_price', 'products_title').
 * @param {string} fieldValue The new value for the field.
 * @param {string} cookies The authentication cookies.
 * @param {object} urlParams Parameters for the URL (region, service, game, typeUrlParam).
 * @return {object} An object with { success: boolean, message: string }.
 * @private
 */
function _sendG2gFieldUpdate(listingId, fieldName, fieldValue, cookies, urlParams) {
  const url = `https://www.g2g.com/sell/updateListing?region=${urlParams.region}&service=${urlParams.service}&game=${urlParams.game}&type=${urlParams.typeUrlParam}`;
  const payload = {
    'name': fieldName,
    'value': fieldValue,
    'pk': listingId,
    'type': 'single',
    'ids': '',
    'scenario': 'update'
  };
  const encodedPayload = Object.keys(payload).map(key => encodeURIComponent(key) + '=' + encodeURIComponent(payload[key])).join('&');
  const headers = {
    "accept": "application/json, text/javascript, */*; q=0.01",
    "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    "x-requested-with": "XMLHttpRequest",
    "Cookie": cookies,
     // Добавляем заголовки, которые были в успешных запросах обновления
    "Referer": `https://www.g2g.com/sell/manage?service=${urlParams.service}&game=${urlParams.game}&region=${urlParams.region}`, // Referer из примеров
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" // User-Agent из примеров
  };
  const options = {
    'method' : 'post',
    'contentType': 'application/x-www-form-urlencoded; charset=UTF-8',
    'headers' : headers,
    'payload' : encodedPayload,
    'muteHttpExceptions': true
  };

  Logger.log(`Отправка G2G Update: ID=${listingId}, Поле=${fieldName}, Значение=${fieldValue.substring(0,50)}...`);
  // Logger.log("Headers: " + JSON.stringify(headers)); // Раскомментируйте для детальной отладки заголовков
  // Logger.log("Payload: " + encodedPayload); // Раскомментируйте для детальной отладки payload

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    Logger.log(`Ответ G2G (${fieldName}): Код ${responseCode}, Текст: ${responseText.substring(0, 200)}...`);

    if (responseCode === 200) {
      try {
        const responseData = JSON.parse(responseText);
        if (responseData.success === true) {
          return { success: true, message: `${fieldName} успешно обновлено.` };
        } else {
          const errorMsg = responseData.msg || `Неизвестная ошибка (success не true)`;
          Logger.log(`Ошибка G2G API (${fieldName}): ${errorMsg}. Ответ: ${responseText}`);
          return { success: false, message: `Ошибка ${fieldName}: ${errorMsg}` };
        }
      } catch (e) {
        Logger.log(`Ошибка парсинга JSON (${fieldName}): ${e}. Ответ: ${responseText}`);
        if (responseText.toLowerCase().includes('<title>login</title>') || responseText.toLowerCase().includes('sign in page')) {
          return { success: false, message: `Ошибка ${fieldName}: Сессия истекла.` };
        } else {
          return { success: false, message: `Ошибка ${fieldName}: Не JSON ответ.` };
        }
      }
    } else {
      Logger.log(`Ошибка HTTP (${fieldName}): Код ${responseCode}. Ответ: ${responseText}`);
      return { success: false, message: `Ошибка ${fieldName}: HTTP ${responseCode}` };
    }
  } catch (error) {
    Logger.log(`Критическая ошибка (${fieldName}): ${error}`);
    return { success: false, message: `Ошибка ${fieldName}: ${error.toString()}` };
  }
}


/**
 * Sends a request to activate or deactivate a G2G listing.
 * @param {string} listingId The listing ID (pk).
 * @param {string} actionType The action ('relist' or 'deactive').
 * @param {string} cookies The authentication cookies.
 * @param {object} urlParams Parameters for the URL (region, service, game, typeUrlParam).
 * @return {object} An object with { success: boolean, message: string }.
 * @private
 */
function _sendG2gAction(listingId, actionType, cookies, urlParams) {
  const url = `https://www.g2g.com/sell/productAction?region=${urlParams.region}&service=${urlParams.service}&game=${urlParams.game}&type=${urlParams.typeUrlParam}`;
  const payload = {
    'listingId': listingId,
    'ids': '', // Кажется, всегда пустой в примерах
    'actionType': actionType
  };
  const encodedPayload = Object.keys(payload).map(key => encodeURIComponent(key) + '=' + encodeURIComponent(payload[key])).join('&');
  const headers = {
    "accept": "application/json, text/javascript, */*; q=0.01", // Как в примерах productAction
    "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    "x-requested-with": "XMLHttpRequest",
    "Cookie": cookies,
    "Referer": `https://www.g2g.com/sell/manage?service=${urlParams.service}&game=${urlParams.game}&region=${urlParams.region}`,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  };
   const options = {
    'method' : 'post',
    'contentType': 'application/x-www-form-urlencoded; charset=UTF-8',
    'headers' : headers,
    'payload' : encodedPayload,
    'muteHttpExceptions': true
  };

  Logger.log(`Отправка G2G Action: ID=${listingId}, Действие=${actionType}`);

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    Logger.log(`Ответ G2G (${actionType}): Код ${responseCode}, Текст: ${responseText.substring(0, 200)}...`);

    // Анализируем ответ - **ВАЖНО: структура ответа productAction может отличаться!**
    // Пока предполагаем, что она такая же, как у updateListing ({success: true/false, msg: ...})
    // Если будут ошибки, нужно смотреть на реальный ответ в логах.
    if (responseCode === 200) {
      try {
        const responseData = JSON.parse(responseText);
        // G2G может возвращать разные структуры для success/error
        // Проверяем на "result": 1 для productAction
        if (responseData.result === 1) {
           // Используем infoMsg из ответа, если есть
           const successMsg = responseData.infoMsg || `Статус (${actionType}) успешно обновлен.`;
           return { success: true, message: successMsg };
        } else {
          // Проверяем старые форматы на всякий случай
          if (responseData.success === true || responseData.status == '1' || responseData.status == 1) {
             return { success: true, message: `Статус (${actionType}) успешно обновлен (старый формат).` };
          }
          // Пробуем извлечь сообщение об ошибке из разных возможных полей
          const errorMsg = responseData.msg || responseData.message || responseData.error || responseData.infoMsg || `Неизвестная ошибка API`;

          // --- НОВАЯ ПРОВЕРКА на "Invalid offer status" --- 
          if (responseData.result === 0 && responseData.infoMsg === "Invalid offer status") {
             Logger.log(`Статус (${actionType}) не изменен, так как лот уже был в этом состоянии.`);
             return { success: true, message: `Статус уже был '${actionType}'.` }; // Считаем это успехом
          }
          // --- КОНЕЦ ПРОВЕРКИ --- 

          Logger.log(`Ошибка G2G API (${actionType}): ${errorMsg}. Ответ: ${responseText}`);
          return { success: false, message: `Ошибка статуса (${actionType}): ${errorMsg}` };
        }
      } catch (e) {
        Logger.log(`Ошибка парсинга JSON (${actionType}): ${e}. Ответ: ${responseText}`);
         if (responseText.toLowerCase().includes('<title>login</title>') || responseText.toLowerCase().includes('sign in page')) {
           return { success: false, message: `Ошибка статуса (${actionType}): Сессия истекла.` };
         } else if (responseText.trim() === '') {
           // Иногда G2G может вернуть пустой ответ при успехе для action
           Logger.log(`Пустой ответ (${actionType}), предполагаем успех.`);
           return { success: true, message: `Статус (${actionType}) успешно обновлен (пустой ответ).` };
         }
         else {
           return { success: false, message: `Ошибка статуса (${actionType}): Не JSON ответ.` };
         }
      }
    } else {
      Logger.log(`Ошибка HTTP (${actionType}): Код ${responseCode}. Ответ: ${responseText}`);
      return { success: false, message: `Ошибка статуса (${actionType}): HTTP ${responseCode}` };
    }
  } catch (error) {
    Logger.log(`Критическая ошибка (${actionType}): ${error}`);
    return { success: false, message: `Ошибка статуса (${actionType}): ${error.toString()}` };
  }
}


/**
 * Finds the G2G service type ID by its name using a lookup sheet and caching.
 * @param {string} serviceTypeName The name of the service type (e.g., "____Nerub'ar Palace [Heroic]").
 * @return {string|null} The corresponding ID or null if not found.
 * @private
 */
function _findG2gServiceTypeIdByName(serviceTypeName) {
  if (!serviceTypeName) {
    Logger.log("Имя типа услуги не предоставлено для поиска ID.");
    return null;
  }

  const cache = CacheService.getScriptCache();
  const cacheKey = 'g2g_servicetype_map';
  let serviceTypeMap = null;

  // Попытка получить карту из кэша
  const cachedMap = cache.get(cacheKey);
  if (cachedMap) {
    try {
      serviceTypeMap = JSON.parse(cachedMap);
      Logger.log("Карта сопоставления 'g2gservicetype' загружена из кэша.");
    } catch (e) {
      Logger.log("Ошибка парсинга карты из кэша: " + e);
      // Очищаем невалидный кэш
      cache.remove(cacheKey);
    }
  }

  // Если карты нет в кэше или произошла ошибка, читаем из таблицы
  if (!serviceTypeMap) {
    Logger.log("Карта сопоставления 'g2gservicetype' не найдена в кэше, читаю из таблицы...");
    serviceTypeMap = {};
    try {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      const lookupSheet = ss.getSheetByName('g2gservicetype');
      if (!lookupSheet) {
        Logger.log("Ошибка: Лист 'g2gservicetype' не найден.");
        SpreadsheetApp.getUi().alert("Критическая ошибка: Лист 'g2gservicetype' для сопоставления ID не найден.");
        return null; // Не можем продолжить без листа
      }
      const data = lookupSheet.getDataRange().getValues();
      // Пропускаем заголовок, если он есть (предполагаем, что данные начинаются со строки 2)
      // Но безопаснее будет проверить, не пустая ли строка
      for (let i = 0; i < data.length; i++) {
        const id = String(data[i][0]).trim(); // Колонка A (ID)
        const name = String(data[i][1]).trim(); // Колонка B (Название)
        if (id && name) { // Убедимся, что обе ячейки не пустые
          serviceTypeMap[name] = id;
        }
      }
      // Сохраняем карту в кэш на 6 часов
      cache.put(cacheKey, JSON.stringify(serviceTypeMap), 21600);
      Logger.log(`Карта сопоставления 'g2gservicetype' создана и сохранена в кэш (${Object.keys(serviceTypeMap).length} записей).`);
    } catch (e) {
      Logger.log("Ошибка при чтении или кэшировании листа 'g2gservicetype': " + e);
      // Не выводим alert здесь, чтобы не прерывать постоянно, но возвращаем null
      return null;
    }
  }

  // Ищем ID в карте
  const foundId = serviceTypeMap[serviceTypeName.trim()]; // Ищем по очищенному имени

  if (foundId) {
    Logger.log(`Найден ID '${foundId}' для типа услуги '${serviceTypeName}'`);
    return foundId;
  } else {
    Logger.log(`ID для типа услуги '${serviceTypeName}' не найден на листе 'g2gservicetype'.`);
    // Можно раскомментировать для отладки содержимого карты:
    // Logger.log("Текущая карта сопоставления: " + JSON.stringify(serviceTypeMap));
    return null;
  }
}


/**
 * Sends a request to update a specific attribute of a G2G listing using the updateAttributes endpoint.
 * @param {string} listingId The listing ID (c2c_products_listing_id).
 * @param {string} attributeKey The key of the attribute (e.g., 'rune').
 * @param {string} attributeValueId The numeric ID of the attribute value.
 * @param {string} cookies The authentication cookies.
 * @param {object} urlParams Parameters for the URL (region, service, game, typeUrlParam).
 * @return {object} An object with { success: boolean, message: string }.
 * @private
 */
function _sendG2gAttributeUpdate(listingId, attributeKey, attributeValueId, cookies, urlParams) {
   // **ВАЖНО**: Эндпоинт и структура payload основаны на предоставленном примере fetch.
   // Необходимо проверить корректность эндпоинта и ключей payload.
  const url = `https://www.g2g.com/sell/updateAttributes?region=${urlParams.region}&service=${urlParams.service}&game=${urlParams.game}&type=${urlParams.typeUrlParam}`;
  const payload = {
    'name': 'value', // Как в примере
    'value': attributeValueId,
    'pk[c2c_products_listing_id]': listingId,
    'pk[key]': attributeKey, // Ключ атрибута (например, 'rune')
    'type': 'single', // Как в примере
    'ids': '', // Как в примере
    'scenario': 'update' // Как в примере
  };
  const encodedPayload = Object.keys(payload).map(key => encodeURIComponent(key) + '=' + encodeURIComponent(payload[key])).join('&');
  const headers = {
    "accept": "application/json, text/javascript, */*; q=0.01",
    "accept-language": "ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7",
    "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
    "x-requested-with": "XMLHttpRequest",
    "Cookie": cookies,
    "Referer": `https://www.g2g.com/sell/manage?service=${urlParams.service}&game=${urlParams.game}&region=${urlParams.region}`,
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  };
   const options = {
    'method' : 'post',
    'contentType': 'application/x-www-form-urlencoded; charset=UTF-8',
    'headers' : headers,
    'payload' : encodedPayload,
    'muteHttpExceptions': true
  };

  Logger.log(`Отправка G2G Attribute Update: ID=${listingId}, Key=${attributeKey}, ValueID=${attributeValueId}`);
  // Logger.log("Attribute Update Payload: " + encodedPayload); // Для отладки

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    Logger.log(`Ответ G2G (Attribute Update ${attributeKey}): Код ${responseCode}, Текст: ${responseText.substring(0, 200)}...`);

    // --- Анализ ответа ---
    // Предполагаем, что ответ может быть похож на productAction или updateListing,
    // но может и отличаться. Проверяем разные варианты успеха.
    if (responseCode === 200) {
      try {
        // Если ответ пустой, но код 200, считаем успехом (бывает у G2G)
        if (responseText.trim() === '') {
           Logger.log(`Пустой ответ (Attribute Update ${attributeKey}), предполагаем успех.`);
           return { success: true, message: `Атрибут '${attributeKey}' успешно обновлен (пустой ответ).` };
        }

        const responseData = JSON.parse(responseText);

        // Проверяем стандартные форматы успеха G2G
        if (responseData.result === 1 || responseData.success === true || responseData.status == '1' || responseData.status == 1) {
           const successMsg = responseData.infoMsg || responseData.msg || `Атрибут '${attributeKey}' успешно обновлен.`;
           return { success: true, message: successMsg };
        } else {
          const errorMsg = responseData.msg || responseData.infoMsg || responseData.message || responseData.error || `Неизвестная ошибка API`;
          Logger.log(`Ошибка G2G API (Attribute Update ${attributeKey}): ${errorMsg}. Ответ: ${responseText}`);
          return { success: false, message: `Ошибка атрибута '${attributeKey}': ${errorMsg}` };
        }
      } catch (e) {
        Logger.log(`Ошибка парсинга JSON (Attribute Update ${attributeKey}): ${e}. Ответ: ${responseText}`);
         if (responseText.toLowerCase().includes('<title>login</title>') || responseText.toLowerCase().includes('sign in page')) {
           return { success: false, message: `Ошибка атрибута '${attributeKey}': Сессия истекла.` };
         } else {
           // Если парсинг не удался, но текст не похож на страницу логина,
           // сложно определить успех. Возвращаем ошибку.
           return { success: false, message: `Ошибка атрибута '${attributeKey}': Не JSON ответ.` };
         }
      }
    } else {
      Logger.log(`Ошибка HTTP (Attribute Update ${attributeKey}): Код ${responseCode}. Ответ: ${responseText}`);
      return { success: false, message: `Ошибка атрибута '${attributeKey}': HTTP ${responseCode}` };
    }
  } catch (error) {
    Logger.log(`Критическая ошибка (Attribute Update ${attributeKey}): ${error}`);
    return { success: false, message: `Ошибка атрибута '${attributeKey}': ${error.toString()}` };
  }
}


function updateG2gListing() {
    const sheet = SpreadsheetApp.getActiveSheet();
    const activeRow = sheet.getActiveRange().getRow();
    const ui = SpreadsheetApp.getUi();

    // --- НАСТРОЙКИ СТОЛБЦОВ (ПОЖАЛУЙСТА, ПРОВЕРЬТЕ!) ---
    const statusColumn = 2;       // B колонка для чекбокса Активности (true = снять, false = выставить)
    const listingIdColumn = 5;    // E колонка для ID лота (pk)
    const priceColumn = 3;        // C колонка для Цены (value)
    const titleColumn = 12;       // L колонка для Названия (products_title)
    const serviceTypeColumn = 8;  // H колонка для Названия Типа Услуги (для поиска ID атрибута)
    const descriptionColumn = 14; // N колонка для Описания (products_description) - ** ПРОВЕРЬТЕ ЭТОТ НОМЕР! **

    // Ключ атрибута для типа услуги (пока хардкод, основанный на примере)
    const serviceTypeAttributeKey = 'rune';

    // Параметры для URL (пока хардкод из примера)
    const urlParams = {
        region: "41709",
        service: "18",
        game: "2299",
        typeUrlParam: "0"
    };
    // --- КОНЕЦ НАСТРОЕК ---

    // Получаем данные из таблицы
    const isChecked = sheet.getRange(activeRow, statusColumn).getValue(); // Читаем чекбокс B
    const listingId = String(sheet.getRange(activeRow, listingIdColumn).getValue());
    const newPrice = String(sheet.getRange(activeRow, priceColumn).getValue());
    const newTitle = String(sheet.getRange(activeRow, titleColumn).getValue());
    const newDescription = String(sheet.getRange(activeRow, descriptionColumn).getValue());
    const serviceTypeName = String(sheet.getRange(activeRow, serviceTypeColumn).getValue()); // Читаем название типа услуги из колонки H


    // Проверка ID
    if (!listingId) {
        ui.alert("Ошибка: ID лота (pk) не найден в колонке " + String.fromCharCode(64 + listingIdColumn) + " для строки " + activeRow);
        return;
    }
    // Проверяем, что хотя бы одно поле для обновления заполнено (чекбокс не считаем, т.к. его статус всегда обновляем)
     if ((!newPrice || isNaN(parseFloat(newPrice))) && !newTitle && !newDescription) {
        Logger.log(`Нет данных для обновления полей Цена/Название/Описание для лота ${listingId}. Будет обновлен только статус.`);
        // Не выходим, позволяем обновить только статус
    }

    // Получаем cookies
    const props = PropertiesService.getScriptProperties();
    const cookies = props.getProperty('g2g_cookies');
    if (!cookies) {
      ui.alert("Ошибка: Cookies для G2G не найдены. Пожалуйста, используйте меню 'G2G -> Установить Cookies'.");
      return;
    }

    SpreadsheetApp.getActiveSpreadsheet().toast(`Обновление лота G2G ${listingId}...`, "Выполнение", 5);

    const results = [];
    let overallSuccess = true;

    // 1. Обновляем Цену (если она есть)
    if (newPrice && !isNaN(parseFloat(newPrice))) {
        const priceResult = _sendG2gFieldUpdate(listingId, 'products_price', newPrice, cookies, urlParams);
        results.push(`Цена: ${priceResult.success ? '✅ Успех' : '❌ ' + priceResult.message}`);
        if (!priceResult.success) overallSuccess = false;
        Utilities.sleep(500); // Пауза
    } else {
        results.push("Цена: Пропущено (нет данных)");
    }

    // 2. Обновляем Название (если оно есть)
    if (newTitle) {
        const titleResult = _sendG2gFieldUpdate(listingId, 'products_title', newTitle, cookies, urlParams);
        results.push(`Название: ${titleResult.success ? '✅ Успех' : '❌ ' + titleResult.message}`);
         if (!titleResult.success) overallSuccess = false;
        Utilities.sleep(500); // Пауза
    } else {
         results.push("Название: Пропущено (нет данных)");
    }

    // 3. Обновляем Описание (если оно есть)
    if (newDescription) {
        const descResult = _sendG2gFieldUpdate(listingId, 'products_description', newDescription, cookies, urlParams);
        results.push(`Описание: ${descResult.success ? '✅ Успех' : '❌ ' + descResult.message}`);
         if (!descResult.success) overallSuccess = false;
         Utilities.sleep(500); // Пауза
    } else {
        results.push("Описание: Пропущено (нет данных)");
    }

    // 4. Обновляем Атрибут Типа Услуги (если название указано)
    if (serviceTypeName) {
        const serviceTypeId = _findG2gServiceTypeIdByName(serviceTypeName);
        if (serviceTypeId) {
            // Отправляем запрос на обновление атрибута
            const attributeResult = _sendG2gAttributeUpdate(listingId, serviceTypeAttributeKey, serviceTypeId, cookies, urlParams);
            results.push(`Тип услуги (${serviceTypeName}): ${attributeResult.success ? '✅ Успех' : '❌ ' + attributeResult.message}`);
            if (!attributeResult.success) overallSuccess = false;
            Utilities.sleep(500); // Пауза
        } else {
            // ID не найден, записываем ошибку
            const errorMsg = `ID для типа услуги '${serviceTypeName}' не найден в справочнике 'g2gservicetype'.`;
            results.push(`Тип услуги (${serviceTypeName}): ❌ ${errorMsg}`);
            Logger.log(errorMsg);
            overallSuccess = false;
            // Не ставим паузу, т.к. запроса не было
        }
    } else {
        results.push("Тип услуги: Пропущено (нет названия в колонке H)");
    }

    // 5. Обновляем Статус (Активировать/Деактивировать)
    // Если чекбокс отмечен (true), то деактивируем ('deactive')
    // Если чекбокс не отмечен (false), то активируем ('relist')
    const actionType = isChecked ? 'deactive' : 'relist';
    const statusResult = _sendG2gAction(listingId, actionType, cookies, urlParams);
    results.push(`Статус (${actionType}): ${statusResult.success ? '✅ Успех' : '❌ ' + statusResult.message}`);
    if (!statusResult.success) overallSuccess = false;


    // Выводим сводный результат
    const summaryMessage = `Результаты обновления лота ${listingId}:\n- ${results.join('\n- ')}`;
    Logger.log(summaryMessage);
    if (overallSuccess) {
        SpreadsheetApp.getActiveSpreadsheet().toast(`Лот ${listingId} обновлен!`, "G2G Успех ✅", 5);
    } else {
        // Используем alert для ошибок, чтобы пользователь точно увидел
        ui.alert(summaryMessage);
    }
}