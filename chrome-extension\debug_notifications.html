<!DOCTYPE html>
<html>
<head>
    <title>Диагностика уведомлений G2G</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Диагностика уведомлений G2G Message Monitor</h1>
    
    <div class="section">
        <h2>1. Проверка разрешений браузера</h2>
        <div id="browserPermissions">Проверяю...</div>
        <button id="requestPermissionBtn" class="btn-primary">Запросить разрешение</button>
    </div>
    
    <div class="section">
        <h2>2. Тест базовых уведомлений</h2>
        <button id="testBasicNotification" class="btn-success">Тест обычного уведомления</button>
        <button id="testRichNotification" class="btn-success">Тест расширенного уведомления</button>
        <div id="basicTestResult"></div>
    </div>
    
    <div class="section">
        <h2>3. Тест Chrome Extension API</h2>
        <button id="testExtensionNotification" class="btn-warning">Тест через chrome.notifications</button>
        <div id="extensionTestResult"></div>
    </div>
    
    <div class="section">
        <h2>4. Проверка системных настроек</h2>
        <div id="systemCheck">
            <p><strong>Windows:</strong> Проверьте Настройки → Система → Уведомления и действия</p>
            <p><strong>Chrome:</strong> Настройки → Конфиденциальность и безопасность → Настройки сайта → Уведомления</p>
            <p><strong>Расширение:</strong> chrome://extensions/ → G2G Message Monitor → Подробности → Разрешения</p>
        </div>
    </div>
    
    <div class="section">
        <h2>5. Логи и отладка</h2>
        <button id="showLogs" class="btn-primary">Показать логи консоли</button>
        <button id="clearLogs" class="btn-warning">Очистить логи</button>
        <pre id="logs">Логи появятся здесь...</pre>
    </div>

    <script>
        const logs = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logs.push(logEntry);
            console.log(logEntry);
            updateLogsDisplay();
        }
        
        function updateLogsDisplay() {
            document.getElementById('logs').textContent = logs.slice(-20).join('\n');
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // Проверка разрешений браузера
        function checkBrowserPermissions() {
            if (!('Notification' in window)) {
                setStatus('browserPermissions', '❌ Браузер не поддерживает уведомления', 'error');
                log('Браузер не поддерживает Notification API', 'error');
                return;
            }
            
            switch(Notification.permission) {
                case 'granted':
                    setStatus('browserPermissions', '✅ Разрешения предоставлены', 'success');
                    log('Разрешения на уведомления предоставлены', 'success');
                    break;
                case 'denied':
                    setStatus('browserPermissions', '❌ Разрешения отклонены', 'error');
                    log('Разрешения на уведомления отклонены', 'error');
                    break;
                default:
                    setStatus('browserPermissions', '⚠️ Разрешения не запрошены', 'warning');
                    log('Разрешения на уведомления не запрошены', 'warning');
            }
        }
        
        // Запрос разрешений
        document.getElementById('requestPermissionBtn').addEventListener('click', function() {
            if (!('Notification' in window)) {
                alert('Браузер не поддерживает уведомления');
                return;
            }
            
            Notification.requestPermission().then(function(permission) {
                log(`Результат запроса разрешений: ${permission}`, permission === 'granted' ? 'success' : 'error');
                checkBrowserPermissions();
            });
        });
        
        // Тест обычного уведомления
        document.getElementById('testBasicNotification').addEventListener('click', function() {
            if (Notification.permission !== 'granted') {
                setStatus('basicTestResult', '❌ Сначала предоставьте разрешения', 'error');
                return;
            }
            
            try {
                const notification = new Notification('G2G Test', {
                    body: 'Тестовое уведомление работает!',
                    tag: 'test-basic'
                });
                
                notification.onclick = function() {
                    log('Клик по обычному уведомлению', 'success');
                    notification.close();
                };
                
                setTimeout(() => notification.close(), 5000);
                
                setStatus('basicTestResult', '✅ Обычное уведомление отправлено', 'success');
                log('Обычное уведомление создано успешно', 'success');
                
            } catch (error) {
                setStatus('basicTestResult', `❌ Ошибка: ${error.message}`, 'error');
                log(`Ошибка создания обычного уведомления: ${error.message}`, 'error');
            }
        });
        
        // Тест расширенного уведомления
        document.getElementById('testRichNotification').addEventListener('click', function() {
            if (Notification.permission !== 'granted') {
                setStatus('basicTestResult', '❌ Сначала предоставьте разрешения', 'error');
                return;
            }
            
            try {
                const notification = new Notification('G2G Rich Test', {
                    body: 'Расширенное уведомление с иконкой',
                    icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                    tag: 'test-rich',
                    requireInteraction: false
                });
                
                notification.onclick = function() {
                    log('Клик по расширенному уведомлению', 'success');
                    notification.close();
                };
                
                setTimeout(() => notification.close(), 5000);
                
                setStatus('basicTestResult', '✅ Расширенное уведомление отправлено', 'success');
                log('Расширенное уведомление создано успешно', 'success');
                
            } catch (error) {
                setStatus('basicTestResult', `❌ Ошибка: ${error.message}`, 'error');
                log(`Ошибка создания расширенного уведомления: ${error.message}`, 'error');
            }
        });
        
        // Тест через Chrome Extension API
        document.getElementById('testExtensionNotification').addEventListener('click', function() {
            if (typeof chrome === 'undefined' || !chrome.notifications) {
                setStatus('extensionTestResult', '❌ Chrome Extension API недоступен', 'error');
                log('Chrome Extension API недоступен', 'error');
                return;
            }
            
            chrome.notifications.create('test-extension', {
                type: 'basic',
                iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                title: 'G2G Extension Test',
                message: 'Тест через chrome.notifications API'
            }, function(notificationId) {
                if (chrome.runtime.lastError) {
                    setStatus('extensionTestResult', `❌ Ошибка: ${chrome.runtime.lastError.message}`, 'error');
                    log(`Ошибка chrome.notifications: ${chrome.runtime.lastError.message}`, 'error');
                } else {
                    setStatus('extensionTestResult', '✅ Extension уведомление отправлено', 'success');
                    log('Chrome Extension уведомление создано успешно', 'success');
                    
                    setTimeout(() => {
                        chrome.notifications.clear(notificationId);
                    }, 5000);
                }
            });
        });
        
        // Показать логи
        document.getElementById('showLogs').addEventListener('click', function() {
            updateLogsDisplay();
        });
        
        // Очистить логи
        document.getElementById('clearLogs').addEventListener('click', function() {
            logs.length = 0;
            updateLogsDisplay();
        });
        
        // Инициализация
        checkBrowserPermissions();
        log('Страница диагностики загружена', 'info');
    </script>
</body>
</html>
