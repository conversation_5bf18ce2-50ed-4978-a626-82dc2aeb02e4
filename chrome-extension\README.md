# G2G Message Monitor

Chrome расширение для автоматического мониторинга непрочитанных сообщений G2G с push-уведомлениями.

## Основные возможности

✅ **Автоматическое извлечение токенов** - находит `access-token` и `session-key` из Sendbird API
✅ **Фоновый мониторинг** - проверяет сообщения каждые 1-15 минут без открытых вкладок
✅ **Push-уведомления** - показывает уведомления при новых сообщениях
✅ **Настраиваемый интервал** - от 1 до 15 минут между проверками
✅ **Автоматическое обновление токенов** - обновляет токены при посещении G2G
✅ **Статистика проверок** - показывает количество успешных/общих проверок

## Установка

### 1. Создание иконок
1. Откройте файл `create_icons.html` в браузере
2. Откройте консоль разработчика (F12)
3. Скачайте все три иконки (icon16.png, icon48.png, icon128.png)
4. Поместите их в папку `chrome-extension/`

### 2. Установка расширения
1. Откройте Chrome
2. Перейдите в `chrome://extensions/`
3. Включите "Режим разработчика" (Developer mode) в правом верхнем углу
4. Нажмите "Загрузить распакованное расширение" (Load unpacked)
5. Выберите папку `chrome-extension/`
6. Расширение должно появиться в списке

## Использование

### 1. Активация токенов
1. Откройте сайт [g2g.com](https://www.g2g.com)
2. Войдите в свой аккаунт
3. Откройте чат или любую страницу с сообщениями
4. Подождите несколько секунд для загрузки Sendbird API

### 2. Извлечение токенов
1. Нажмите на иконку расширения в панели инструментов Chrome
2. Нажмите кнопку "Обновить" если токены не найдены автоматически
3. Если токены найдены, они отобразятся в popup
4. Нажмите "Копировать PowerShell" для получения готового кода

### 3. Настройка автоматического мониторинга
1. **Запуск мониторинга:**
   - Нажмите "Запустить мониторинг" в popup расширения
   - Выберите интервал проверки (1-15 минут)
   - Мониторинг будет работать в фоне даже при закрытых вкладках

2. **Уведомления:**
   - При новых сообщениях появится push-уведомление
   - Уведомления автоматически исчезают через 10 секунд
   - Показывается количество непрочитанных сообщений

3. **Управление:**
   - "Остановить мониторинг" - отключает автоматические проверки
   - "Проверить сейчас" - мгновенная проверка сообщений
   - Статистика показывает успешность проверок

### 4. Использование токенов в PowerShell (опционально)
1. Скопированный PowerShell код содержит все необходимые переменные
2. Вставьте код в PowerShell
3. Запустите скрипт `get_unread_messages.ps1`

## Где расширение ищет токены

1. **Сетевые запросы** - перехватывает заголовки запросов к `*.sendbird.com`
2. **LocalStorage** - ищет ключи содержащие 'sendbird', 'access', 'session'
3. **SessionStorage** - аналогично localStorage
4. **Window объекты** - проверяет `window.SendBird`, `window.sendbird`, `window.sb`
5. **Cookies** - проверяет куки (маловероятно, но возможно)
6. **XMLHttpRequest** - перехватывает заголовки XHR запросов

## Возможные проблемы

### Токены не найдены
- Убедитесь, что вы вошли в аккаунт G2G
- Откройте чат или страницу с сообщениями
- Подождите полной загрузки страницы
- Попробуйте обновить страницу и нажать "Обновить" в расширении

### Расширение не работает
- Проверьте, что включен "Режим разработчика" в chrome://extensions/
- Убедитесь, что все файлы расширения на месте
- Проверьте консоль разработчика на наличие ошибок

### Токены устарели
- Токены могут истекать через некоторое время
- Просто извлеките их заново через расширение
- Рекомендуется обновлять токены перед каждым использованием

## Безопасность

- Расширение работает только на сайте G2G
- Токены сохраняются локально в браузере
- Никакие данные не отправляются на внешние серверы
- Исходный код открыт для проверки

## Структура файлов

```
chrome-extension/
├── manifest.json       # Конфигурация расширения
├── background.js       # Фоновый скрипт (перехват запросов)
├── content.js         # Контентный скрипт (поиск на странице)
├── popup.html         # HTML интерфейса
├── popup.js           # JavaScript интерфейса
├── create_icons.html  # Генератор иконок
├── icon16.png         # Иконка 16x16
├── icon48.png         # Иконка 48x48
├── icon128.png        # Иконка 128x128
└── README.md          # Эта инструкция
```

## Отладка

Для отладки откройте консоль разработчика на странице G2G и найдите сообщения от "G2G Token Extractor".

Также можно проверить фоновый скрипт:
1. Перейдите в `chrome://extensions/`
2. Найдите расширение
3. Нажмите "Подробности"
4. Нажмите "Просмотр в инспекторе фоновых страниц"
