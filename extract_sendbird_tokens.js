// Скрипт для извлечения Sendbird токенов из браузера G2G
// Выполните этот код в консоли браузера на сайте g2g.com

(function() {
    console.log("=== ИЗВЛЕЧЕНИЕ SENDBIRD ТОКЕНОВ G2G ===");
    
    // Попытка найти токены в localStorage
    console.log("\n1. Проверка localStorage:");
    const localStorageKeys = Object.keys(localStorage);
    const sendbirdKeys = localStorageKeys.filter(key => 
        key.toLowerCase().includes('sendbird') || 
        key.toLowerCase().includes('access') ||
        key.toLowerCase().includes('session') ||
        key.toLowerCase().includes('token')
    );
    
    if (sendbirdKeys.length > 0) {
        sendbirdKeys.forEach(key => {
            console.log(`  ${key}: ${localStorage.getItem(key)}`);
        });
    } else {
        console.log("  Sendbird токены в localStorage не найдены");
    }
    
    // Попытка найти токены в sessionStorage
    console.log("\n2. Проверка sessionStorage:");
    const sessionStorageKeys = Object.keys(sessionStorage);
    const sendbirdSessionKeys = sessionStorageKeys.filter(key => 
        key.toLowerCase().includes('sendbird') || 
        key.toLowerCase().includes('access') ||
        key.toLowerCase().includes('session') ||
        key.toLowerCase().includes('token')
    );
    
    if (sendbirdSessionKeys.length > 0) {
        sendbirdSessionKeys.forEach(key => {
            console.log(`  ${key}: ${sessionStorage.getItem(key)}`);
        });
    } else {
        console.log("  Sendbird токены в sessionStorage не найдены");
    }
    
    // Попытка найти Sendbird объект в window
    console.log("\n3. Проверка глобальных объектов:");
    if (window.SendBird || window.sendbird || window.sb) {
        const sb = window.SendBird || window.sendbird || window.sb;
        console.log("  Найден объект Sendbird:", sb);
        
        // Попытка получить текущего пользователя и токены
        try {
            if (sb.currentUser) {
                console.log("  Current User:", sb.currentUser);
                console.log("  User ID:", sb.currentUser.userId);
                console.log("  Access Token:", sb.currentUser.accessToken);
            }
            
            if (sb.getAccessToken) {
                console.log("  Access Token (метод):", sb.getAccessToken());
            }
            
            if (sb.getSessionToken) {
                console.log("  Session Token (метод):", sb.getSessionToken());
            }
        } catch (e) {
            console.log("  Ошибка при получении токенов из Sendbird объекта:", e);
        }
    } else {
        console.log("  Sendbird объект не найден в window");
    }
    
    // Перехват сетевых запросов (если возможно)
    console.log("\n4. Инструкция по перехвату токенов:");
    console.log("  Откройте DevTools -> Network");
    console.log("  Отфильтруйте по 'sendbird.com'");
    console.log("  Найдите любой запрос к API Sendbird");
    console.log("  В заголовках запроса найдите:");
    console.log("    - access-token: ваш_access_token");
    console.log("    - session-key: ваш_session_key");
    
    // Попытка найти токены в куках (хотя их там обычно нет)
    console.log("\n5. Проверка cookies:");
    const cookies = document.cookie.split(';');
    const sendbirdCookies = cookies.filter(cookie => 
        cookie.toLowerCase().includes('sendbird') ||
        cookie.toLowerCase().includes('access') ||
        cookie.toLowerCase().includes('session')
    );
    
    if (sendbirdCookies.length > 0) {
        sendbirdCookies.forEach(cookie => {
            console.log(`  ${cookie.trim()}`);
        });
    } else {
        console.log("  Sendbird токены в cookies не найдены");
    }
    
    console.log("\n=== ГОТОВЫЙ POWERSHELL КОД ===");
    console.log("# Скопируйте найденные токены в эти переменные:");
    console.log('$AccessToken = "ваш_access_token_здесь"');
    console.log('$SessionKey = "ваш_session_key_здесь"');
    console.log('$UserId = "ваш_user_id_здесь"');
    console.log("");
    console.log("# Затем запустите:");
    console.log('.\\get_unread_messages.ps1 -AccessToken $AccessToken -SessionKey $SessionKey -UserId $UserId');
    
})();
