<!DOCTYPE html>
<html>
<head>
    <title>Тест уведомлений Chrome</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
        }
        .btn-primary {
            background-color: #1a73e8;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Тест уведомлений для G2G Message Monitor</h1>
    
    <p>Эта страница поможет проверить работу уведомлений в браузере.</p>
    
    <button id="requestPermission" class="btn-primary">Запросить разрешение на уведомления</button>
    <button id="testNotification" class="btn-success">Показать тестовое уведомление</button>
    
    <div id="status"></div>
    
    <h2>Инструкции:</h2>
    <ol>
        <li>Нажмите "Запросить разрешение на уведомления"</li>
        <li>Разрешите уведомления в браузере</li>
        <li>Нажмите "Показать тестовое уведомление"</li>
        <li>Если уведомление появилось - всё работает правильно</li>
    </ol>
    
    <h2>Возможные проблемы:</h2>
    <ul>
        <li><strong>Уведомления заблокированы:</strong> Проверьте настройки сайта в браузере</li>
        <li><strong>Уведомления не появляются:</strong> Проверьте системные настройки уведомлений</li>
        <li><strong>Windows:</strong> Убедитесь что уведомления включены в настройках Windows</li>
    </ul>

    <script>
        const statusEl = document.getElementById('status');
        
        function showStatus(message, isError = false) {
            statusEl.textContent = message;
            statusEl.className = `status ${isError ? 'error' : 'success'}`;
        }
        
        document.getElementById('requestPermission').addEventListener('click', function() {
            if (!('Notification' in window)) {
                showStatus('Этот браузер не поддерживает уведомления', true);
                return;
            }
            
            if (Notification.permission === 'granted') {
                showStatus('Разрешение на уведомления уже предоставлено');
                return;
            }
            
            if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(function(permission) {
                    if (permission === 'granted') {
                        showStatus('Разрешение на уведомления предоставлено!');
                    } else {
                        showStatus('Разрешение на уведомления отклонено', true);
                    }
                });
            } else {
                showStatus('Уведомления заблокированы. Разблокируйте их в настройках браузера', true);
            }
        });
        
        document.getElementById('testNotification').addEventListener('click', function() {
            if (!('Notification' in window)) {
                showStatus('Этот браузер не поддерживает уведомления', true);
                return;
            }
            
            if (Notification.permission !== 'granted') {
                showStatus('Сначала предоставьте разрешение на уведомления', true);
                return;
            }
            
            const notification = new Notification('G2G - Тестовое уведомление', {
                body: 'Если вы видите это уведомление, то всё работает правильно!',
                icon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
                tag: 'test-notification'
            });
            
            notification.onclick = function() {
                showStatus('Уведомление работает! Клик обработан.');
                notification.close();
            };
            
            setTimeout(function() {
                notification.close();
            }, 5000);
            
            showStatus('Тестовое уведомление отправлено');
        });
        
        // Проверяем текущий статус разрешений
        if ('Notification' in window) {
            switch(Notification.permission) {
                case 'granted':
                    showStatus('Уведомления разрешены');
                    break;
                case 'denied':
                    showStatus('Уведомления заблокированы', true);
                    break;
                default:
                    showStatus('Нажмите кнопку для запроса разрешения на уведомления');
            }
        } else {
            showStatus('Браузер не поддерживает уведомления', true);
        }
    </script>
</body>
</html>
