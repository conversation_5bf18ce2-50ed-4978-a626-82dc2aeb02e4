# 🔔 Решение проблем с уведомлениями G2G Message Monitor

## 🚨 Проблема: Уведомления не показываются

### Быстрая диагностика:

1. **Откройте popup расширения** и нажмите **"Тест уведомления"**
2. **Откройте `debug_notifications.html`** для полной диагностики
3. **Проверьте консоль** background script: `chrome://extensions/` → G2G Message Monitor → "Просмотр в инспекторе фоновых страниц"

### Возможные причины и решения:

#### 1. ❌ Разрешения браузера
**Проблема:** Chrome не разрешает уведомления для расширения

**Решение:**
```
1. Откройте chrome://settings/content/notifications
2. Найдите "G2G Message Monitor" или ваш домен
3. Убедитесь что установлено "Разрешить"
4. Перезапустите расширение
```

#### 2. ❌ Системные уведомления Windows
**Проблема:** Windows блокирует уведомления Chrome

**Решение:**
```
1. Настройки Windows → Система → Уведомления и действия
2. Убедитесь что уведомления включены
3. Найдите Google Chrome в списке приложений
4. Включите уведомления для Chrome
```

#### 3. ❌ Режим "Не беспокоить"
**Проблема:** Включен режим "Фокусировка внимания" или "Не беспокоить"

**Решение:**
```
1. Проверьте панель уведомлений Windows (правый нижний угол)
2. Отключите "Фокусировка внимания" если включена
3. Проверьте настройки уведомлений в Chrome
```

#### 4. ❌ Проблемы с иконкой
**Проблема:** Расширение не может загрузить иконку для уведомления

**Решение:**
```
1. Убедитесь что файлы icon16.png, icon48.png, icon128.png существуют
2. Или используйте встроенную base64 иконку (уже исправлено в коде)
```

#### 5. ❌ Ошибки в консоли
**Проблема:** JavaScript ошибки в background script

**Решение:**
```
1. Откройте chrome://extensions/
2. Найдите G2G Message Monitor
3. Нажмите "Подробности"
4. Нажмите "Просмотр в инспекторе фоновых страниц"
5. Проверьте консоль на ошибки
```

### Пошаговая диагностика:

#### Шаг 1: Проверка базовых уведомлений
```javascript
// Выполните в консоли любой страницы:
if (Notification.permission === 'granted') {
    new Notification('Тест', { body: 'Работает!' });
} else {
    console.log('Разрешения не предоставлены:', Notification.permission);
}
```

#### Шаг 2: Проверка Chrome Extension API
```javascript
// Выполните в консоли background script расширения:
chrome.notifications.create('test', {
    type: 'basic',
    iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    title: 'Тест',
    message: 'Работает!'
}, (id) => console.log('Создано:', id, chrome.runtime.lastError));
```

#### Шаг 3: Проверка логов расширения
Ищите в консоли background script сообщения:
- `✅ Уведомление создано успешно`
- `Ошибка создания уведомления`
- `Показываем уведомление: unreadCount=X`

### Альтернативные решения:

#### 1. Badge уведомления
Если push-уведомления не работают, расширение показывает красный badge с числом:
- Смотрите на иконку расширения в панели инструментов
- Красная цифра = количество непрочитанных сообщений

#### 2. Звуковые уведомления
Можно добавить звук в код уведомления:
```javascript
// В функции showNotification добавить:
const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
audio.play().catch(() => {}); // Игнорируем ошибки
```

### Финальная проверка:

1. ✅ **Разрешения браузера** предоставлены
2. ✅ **Системные уведомления** включены  
3. ✅ **Режим "Не беспокоить"** отключен
4. ✅ **Консоль background script** без ошибок
5. ✅ **Тест уведомления** в popup работает

### Если ничего не помогает:

1. **Перезапустите расширение:**
   - chrome://extensions/ → G2G Message Monitor → Переключатель OFF/ON

2. **Перезапустите Chrome** полностью

3. **Проверьте другие расширения** - они могут блокировать уведомления

4. **Используйте badge** как альтернативу - красная цифра на иконке расширения

### Логи для отладки:

Включите подробные логи в background.js:
```javascript
// Добавьте в начало функции showNotification:
console.log('🔔 Попытка показать уведомление:', {
    unreadCount,
    permission: Notification?.permission,
    chromeNotifications: !!chrome.notifications
});
```

Эти логи помогут понять на каком этапе происходит сбой.
