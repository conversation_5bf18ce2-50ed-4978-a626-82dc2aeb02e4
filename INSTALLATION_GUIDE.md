# 🚀 G2G Message Monitor - Полная инструкция по установке

## 📋 Что это такое?

**G2G Message Monitor** - это Chrome расширение, которое:
- ✅ Автоматически отслеживает непрочитанные сообщения на G2G
- ✅ Показывает push-уведомления при новых сообщениях  
- ✅ Работает в фоне без открытых вкладок
- ✅ Проверяет сообщения каждые 1-15 минут (настраивается)
- ✅ Автоматически обновляет токены при посещении G2G

## 🛠️ Установка расширения

### Шаг 1: Создание иконок
1. Откройте файл `chrome-extension/create_icons.html` в браузере
2. Откройте консоль разработчика (F12)
3. Скачайте все три иконки:
   - `icon16.png`
   - `icon48.png` 
   - `icon128.png`
4. Поместите их в папку `chrome-extension/`

### Шаг 2: Установка в Chrome
1. Откройте Chrome
2. Перейдите в `chrome://extensions/`
3. Включите **"Режим разработчика"** в правом верхнем углу
4. Нажмите **"Загрузить распакованное расширение"**
5. Выберите папку `chrome-extension/`
6. Расширение появится в списке и в панели инструментов

## 🎯 Первоначальная настройка

### Шаг 1: Получение токенов
1. Откройте [g2g.com](https://www.g2g.com) и войдите в аккаунт
2. Откройте чат или любую страницу с сообщениями
3. Подождите 10-15 секунд для загрузки Sendbird API
4. Нажмите на иконку расширения в панели инструментов
5. Токены должны появиться автоматически

### Шаг 2: Запуск мониторинга
1. В popup расширения выберите интервал проверки (рекомендуется 2-5 минут)
2. Нажмите **"Запустить мониторинг"**
3. Статус изменится на "Активен"
4. Мониторинг будет работать даже при закрытом браузере

### Шаг 3: Настройка уведомлений
1. При первом запуске браузер запросит разрешение на уведомления
2. Нажмите **"Разрешить"**
3. Для тестирования откройте `chrome-extension/test_notifications.html`

## 🔧 Использование

### Основные функции
- **"Запустить/Остановить мониторинг"** - включает/выключает автоматические проверки
- **"Проверить сейчас"** - мгновенная проверка сообщений
- **Интервал проверки** - от 1 до 15 минут между проверками
- **Статистика** - показывает количество успешных проверок

### Уведомления
- Появляются при новых непрочитанных сообщениях
- Показывают количество сообщений
- Автоматически исчезают через 10 секунд
- Работают даже при свернутом браузере

### Автоматическое обновление токенов
- Токены обновляются при каждом посещении G2G
- Мониторинг автоматически перезапускается с новыми токенами
- Не требует ручного вмешательства

## 🐛 Решение проблем

### Токены не найдены
**Причины:**
- Не вошли в аккаунт G2G
- Не открывали чат или сообщения
- Страница не полностью загрузилась

**Решение:**
1. Убедитесь что вошли в аккаунт G2G
2. Откройте чат или страницу с сообщениями
3. Подождите 10-15 секунд
4. Нажмите "Обновить" в расширении

### Уведомления не работают
**Причины:**
- Не предоставлено разрешение на уведомления
- Уведомления отключены в системе
- Браузер работает в фоновом режиме

**Решение:**
1. Проверьте разрешения: `chrome://settings/content/notifications`
2. Убедитесь что уведомления включены в Windows/macOS
3. Протестируйте через `test_notifications.html`

### Мониторинг не запускается
**Причины:**
- Недостаточно токенов
- Неверные токены
- Проблемы с сетью

**Решение:**
1. Обновите токены через "Обновить"
2. Проверьте подключение к интернету
3. Попробуйте "Проверить сейчас"

### Расширение не работает
**Причины:**
- Не включен режим разработчика
- Отсутствуют файлы расширения
- Ошибки в коде

**Решение:**
1. Проверьте `chrome://extensions/` - должен быть включен "Режим разработчика"
2. Убедитесь что все файлы на месте
3. Проверьте консоль на ошибки (F12)

## 📊 Альтернативное использование (PowerShell)

Если не хотите использовать расширение, можете использовать PowerShell скрипты:

```powershell
# Показать информацию о расширении
.\get_unread_messages.ps1 -ShowExtensionInfo

# Разовая проверка
.\get_unread_messages.ps1 -AccessToken "ваш_токен" -SessionKey "ваш_ключ"

# Мониторинг каждые 5 минут
.\monitor_tokens.ps1 -IntervalMinutes 5
```

## 🔒 Безопасность

- ✅ Расширение работает только на G2G
- ✅ Токены сохраняются локально в браузере
- ✅ Никакие данные не отправляются на внешние серверы
- ✅ Исходный код открыт для проверки
- ✅ Использует официальный API Sendbird

## 📁 Структура файлов

```
chrome-extension/
├── manifest.json           # Конфигурация расширения
├── background.js           # Фоновый мониторинг
├── content.js             # Извлечение токенов
├── popup.html/js          # Интерфейс управления
├── create_icons.html      # Генератор иконок
├── test_notifications.html # Тест уведомлений
├── icon16/48/128.png      # Иконки расширения
└── README.md              # Техническая документация
```

## 🎉 Готово!

После установки расширение будет:
1. Автоматически находить токены при посещении G2G
2. Отслеживать сообщения в фоне
3. Показывать уведомления о новых сообщениях
4. Работать без вашего участия

**Рекомендуемые настройки:**
- Интервал проверки: 2-5 минут
- Уведомления: включены
- Автозапуск мониторинга: да
