{"manifest_version": 3, "name": "G2G Sendbird Token Extractor", "version": "1.0", "description": "Извлекает access-token и session-key для Sendbird API из G2G", "permissions": ["activeTab", "storage", "webRequest", "webRequestBlocking"], "host_permissions": ["*://*.g2g.com/*", "*://*.sendbird.com/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://*.g2g.com/*"], "js": ["content.js"], "run_at": "document_start"}], "action": {"default_popup": "popup.html", "default_title": "G2G Token Extractor"}, "icons": {"16": "icon16.png", "48": "icon48.png", "128": "icon128.png"}}