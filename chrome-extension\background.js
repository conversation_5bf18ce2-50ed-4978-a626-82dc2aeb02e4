// Background script для автоматической проверки сообщений G2G

let capturedTokens = {
  accessToken: null,
  sessionKey: null,
  userId: null,
  appId: null,
  lastUpdated: null
};

let monitoringEnabled = false;
let lastUnreadCount = 0;

// Перехватываем запросы к Sendbird API
chrome.webRequest.onBeforeSendHeaders.addListener(
  function(details) {
    if (details.url.includes('sendbird.com')) {
      console.log('Перехвачен запрос к Sendbird:', details.url);
      
      // Извлекаем токены из заголовков
      if (details.requestHeaders) {
        details.requestHeaders.forEach(header => {
          switch(header.name.toLowerCase()) {
            case 'access-token':
              capturedTokens.accessToken = header.value;
              console.log('Найден access-token:', header.value);
              break;
            case 'session-key':
              capturedTokens.sessionKey = header.value;
              console.log('Найден session-key:', header.value);
              break;
            case 'app-id':
              capturedTokens.appId = header.value;
              console.log('Найден app-id:', header.value);
              break;
          }
        });
        
        // Извлекаем User ID из URL если возможно
        const userIdMatch = details.url.match(/\/users\/(\d+)/);
        if (userIdMatch) {
          capturedTokens.userId = userIdMatch[1];
          console.log('Найден user-id из URL:', userIdMatch[1]);
        }
        
        // Обновляем время последнего обновления
        capturedTokens.lastUpdated = new Date().toISOString();
        
        // Сохраняем в storage
        chrome.storage.local.set({ 'g2g_sendbird_tokens': capturedTokens });
        
        // Уведомляем popup если он открыт
        chrome.runtime.sendMessage({
          type: 'TOKENS_UPDATED',
          tokens: capturedTokens
        }).catch(() => {
          // Игнорируем ошибку если popup не открыт
        });
      }
    }
  },
  {
    urls: ["*://*.sendbird.com/*"]
  },
  ["requestHeaders"]
);

// Обработка сообщений от content script и popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch(message.type) {
    case 'GET_TOKENS':
      sendResponse(capturedTokens);
      break;
      
    case 'TOKENS_FROM_CONTENT':
      // Обновляем токены полученные от content script
      Object.assign(capturedTokens, message.tokens);
      capturedTokens.lastUpdated = new Date().toISOString();
      chrome.storage.local.set({ 'g2g_sendbird_tokens': capturedTokens });
      sendResponse({ success: true });
      break;
      
    case 'CLEAR_TOKENS':
      capturedTokens = {
        accessToken: null,
        sessionKey: null,
        userId: null,
        appId: null,
        lastUpdated: null
      };
      chrome.storage.local.remove('g2g_sendbird_tokens');
      sendResponse({ success: true });
      break;
  }
});

// Загружаем сохраненные токены при запуске
chrome.storage.local.get('g2g_sendbird_tokens', (result) => {
  if (result.g2g_sendbird_tokens) {
    capturedTokens = result.g2g_sendbird_tokens;
    console.log('Загружены сохраненные токены:', capturedTokens);
  }
});
