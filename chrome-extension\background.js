// Background script для автоматического мониторинга сообщений G2G

let capturedTokens = {
  accessToken: null,
  sessionKey: null,
  userId: null,
  appId: null,
  lastUpdated: null
};

let monitoringSettings = {
  enabled: false,
  interval: 1, // минуты
  lastUnreadCount: 0,
  lastCheckTime: null,
  totalChecks: 0,
  successfulChecks: 0
};

// Перехватываем запросы к Sendbird API
chrome.webRequest.onBeforeSendHeaders.addListener(
  function(details) {
    if (details.url.includes('sendbird.com')) {
      console.log('Перехвачен запрос к Sendbird:', details.url);
      
      // Извлекаем токены из заголовков
      if (details.requestHeaders) {
        details.requestHeaders.forEach(header => {
          switch(header.name.toLowerCase()) {
            case 'access-token':
              capturedTokens.accessToken = header.value;
              console.log('Найден access-token:', header.value);
              break;
            case 'session-key':
              capturedTokens.sessionKey = header.value;
              console.log('Найден session-key:', header.value);
              break;
            case 'app-id':
              capturedTokens.appId = header.value;
              console.log('Найден app-id:', header.value);
              break;
          }
        });
        
        // Извлекаем User ID из URL если возможно
        const userIdMatch = details.url.match(/\/users\/(\d+)/);
        if (userIdMatch) {
          capturedTokens.userId = userIdMatch[1];
          console.log('Найден user-id из URL:', userIdMatch[1]);
        }
        
        // Обновляем время последнего обновления
        capturedTokens.lastUpdated = new Date().toISOString();
        
        // Сохраняем в storage
        chrome.storage.local.set({ 'g2g_sendbird_tokens': capturedTokens });
        
        // Уведомляем popup если он открыт
        chrome.runtime.sendMessage({
          type: 'TOKENS_UPDATED',
          tokens: capturedTokens
        }).catch(() => {
          // Игнорируем ошибку если popup не открыт
        });
      }
    }
  },
  {
    urls: ["*://*.sendbird.com/*"]
  },
  ["requestHeaders"]
);

// Обработка сообщений от content script и popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch(message.type) {
    case 'GET_TOKENS':
      sendResponse(capturedTokens);
      break;

    case 'GET_MONITORING_STATUS':
      sendResponse(monitoringSettings);
      break;

    case 'START_MONITORING':
      if (capturedTokens.accessToken && capturedTokens.sessionKey) {
        if (message.interval) {
          monitoringSettings.interval = message.interval;
        }
        startMonitoring();
        sendResponse({ success: true, message: 'Мониторинг запущен' });
      } else {
        sendResponse({ success: false, message: 'Недостаточно токенов для мониторинга' });
      }
      break;

    case 'STOP_MONITORING':
      stopMonitoring();
      sendResponse({ success: true, message: 'Мониторинг остановлен' });
      break;

    case 'CHECK_MESSAGES_NOW':
      checkUnreadMessages(true).then(result => { // true = принудительно показать уведомление
        sendResponse(result);
      });
      return true; // Асинхронный ответ

    case 'TOKENS_FROM_CONTENT':
      // Обновляем токены полученные от content script
      Object.assign(capturedTokens, message.tokens);
      capturedTokens.lastUpdated = new Date().toISOString();
      chrome.storage.local.set({ 'g2g_sendbird_tokens': capturedTokens });

      // Если мониторинг включен и у нас теперь есть все токены, перезапускаем его
      if (monitoringSettings.enabled && capturedTokens.accessToken && capturedTokens.sessionKey) {
        chrome.alarms.clear('checkMessages');
        startMonitoring();
      }

      sendResponse({ success: true });
      break;

    case 'CLEAR_TOKENS':
      capturedTokens = {
        accessToken: null,
        sessionKey: null,
        userId: null,
        appId: null,
        lastUpdated: null
      };
      stopMonitoring(); // Останавливаем мониторинг при очистке токенов
      chrome.storage.local.remove('g2g_sendbird_tokens');
      sendResponse({ success: true });
      break;
  }
});

// Функция для проверки непрочитанных сообщений
async function checkUnreadMessages(forceNotification = false) {
  if (!capturedTokens.accessToken || !capturedTokens.sessionKey || !capturedTokens.userId) {
    console.log('Мониторинг: Недостаточно токенов для проверки');
    return { success: false, error: 'Недостаточно токенов' };
  }

  const url = `https://****************************************.sendbird.com/v3/users/${capturedTokens.userId}/unread_message_count?super_mode=all&include_feed_channel=false`;

  try {
    monitoringSettings.totalChecks++;
    monitoringSettings.lastCheckTime = new Date().toISOString();

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'access-token': capturedTokens.accessToken,
        'app-id': capturedTokens.appId || '34201740-152E-401E-AD8F-5C72EEABA386',
        'session-key': capturedTokens.sessionKey,
        'content-type': 'application/json; charset=utf-8'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const unreadCount = data.unread_count || 0;

    monitoringSettings.successfulChecks++;
    console.log(`Мониторинг: Непрочитанных сообщений: ${unreadCount}`);

    // Показываем уведомление если:
    // 1. Есть новые сообщения (количество изменилось)
    // 2. Или принудительно запрошено (при ручной проверке)
    if (unreadCount > 0 && (unreadCount !== monitoringSettings.lastUnreadCount || forceNotification)) {
      console.log(`Показываем уведомление: unreadCount=${unreadCount}, lastCount=${monitoringSettings.lastUnreadCount}, force=${forceNotification}`);
      showNotification(unreadCount);
    } else if (unreadCount === 0) {
      // Очищаем badge если нет сообщений
      chrome.action.setBadgeText({ text: '' });
    }

    monitoringSettings.lastUnreadCount = unreadCount;
    saveMonitoringSettings();

    return { success: true, unreadCount: unreadCount };

  } catch (error) {
    console.error('Ошибка при проверке сообщений:', error);
    return { success: false, error: error.message };
  }
}

// Функция для показа уведомления
function showNotification(unreadCount) {
  const title = 'G2G - Новые сообщения!';
  const message = `У вас ${unreadCount} непрочитанных сообщений`;

  console.log(`Попытка показать уведомление: ${title} - ${message}`);

  // Создаем уведомление с уникальным ID
  const notificationId = `g2g-notification-${Date.now()}`;

  chrome.notifications.create(notificationId, {
    type: 'basic',
    iconUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // Простая иконка
    title: title,
    message: message,
    priority: 2,
    requireInteraction: false // Позволяет уведомлению исчезнуть автоматически
  }, (notificationId) => {
    if (chrome.runtime.lastError) {
      console.error('Ошибка создания уведомления:', chrome.runtime.lastError);

      // Попробуем альтернативный способ - через action badge
      chrome.action.setBadgeText({ text: unreadCount.toString() });
      chrome.action.setBadgeBackgroundColor({ color: '#ff0000' });
      chrome.action.setTitle({ title: `G2G: ${unreadCount} непрочитанных сообщений` });

    } else {
      console.log(`✅ Уведомление создано успешно: ${notificationId}`);

      // Также обновляем badge
      chrome.action.setBadgeText({ text: unreadCount.toString() });
      chrome.action.setBadgeBackgroundColor({ color: '#ff0000' });

      // Автоматически закрываем уведомление через 8 секунд
      setTimeout(() => {
        chrome.notifications.clear(notificationId, (wasCleared) => {
          console.log(`Уведомление ${notificationId} ${wasCleared ? 'закрыто' : 'уже было закрыто'}`);
        });
      }, 8000);
    }
  });
}

// Функция для запуска мониторинга
function startMonitoring() {
  if (monitoringSettings.enabled) {
    console.log('Мониторинг уже запущен');
    return;
  }

  monitoringSettings.enabled = true;
  console.log(`Запуск мониторинга с интервалом ${monitoringSettings.interval} минут`);

  // Создаем alarm для периодической проверки
  chrome.alarms.create('checkMessages', {
    delayInMinutes: 0.1, // Первая проверка через 6 секунд
    periodInMinutes: monitoringSettings.interval
  });

  saveMonitoringSettings();
}

// Функция для остановки мониторинга
function stopMonitoring() {
  monitoringSettings.enabled = false;
  chrome.alarms.clear('checkMessages');
  console.log('Мониторинг остановлен');
  saveMonitoringSettings();
}

// Обработчик alarm'ов
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'checkMessages' && monitoringSettings.enabled) {
    console.log('⏰ Автоматическая проверка сообщений по расписанию');
    checkUnreadMessages();
  }
});

// Обработчик клика по уведомлению
chrome.notifications.onClicked.addListener((notificationId) => {
  console.log(`Клик по уведомлению: ${notificationId}`);

  // Открываем G2G в новой вкладке
  chrome.tabs.create({
    url: 'https://www.g2g.com/chat',
    active: true
  });

  // Закрываем уведомление
  chrome.notifications.clear(notificationId);

  // Очищаем badge
  chrome.action.setBadgeText({ text: '' });
});

// Обработчик закрытия уведомления
chrome.notifications.onClosed.addListener((notificationId, byUser) => {
  console.log(`Уведомление ${notificationId} закрыто ${byUser ? 'пользователем' : 'автоматически'}`);
});

// Функция для сохранения настроек мониторинга
function saveMonitoringSettings() {
  chrome.storage.local.set({ 'g2g_monitoring_settings': monitoringSettings });
}

// Загружаем сохраненные данные при запуске
chrome.storage.local.get(['g2g_sendbird_tokens', 'g2g_monitoring_settings'], (result) => {
  if (result.g2g_sendbird_tokens) {
    capturedTokens = result.g2g_sendbird_tokens;
    console.log('Загружены сохраненные токены:', capturedTokens);
  }

  if (result.g2g_monitoring_settings) {
    monitoringSettings = { ...monitoringSettings, ...result.g2g_monitoring_settings };
    console.log('Загружены настройки мониторинга:', monitoringSettings);

    // Если мониторинг был включен, запускаем его снова
    if (monitoringSettings.enabled && capturedTokens.accessToken) {
      startMonitoring();
    }
  }
});
