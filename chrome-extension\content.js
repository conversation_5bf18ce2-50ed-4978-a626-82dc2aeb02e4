// Content script для поиска токенов на странице G2G

console.log('G2G Token Extractor: Content script загружен');

// Функция для поиска токенов в различных местах
function extractTokens() {
  const tokens = {};
  
  // 1. Поиск в localStorage
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      
      if (key && (key.includes('sendbird') || key.includes('access') || key.includes('session'))) {
        console.log(`LocalStorage найден: ${key} = ${value}`);
        
        // Попытка парсинга JSON
        try {
          const parsed = JSON.parse(value);
          if (parsed.accessToken) tokens.accessToken = parsed.accessToken;
          if (parsed.sessionKey) tokens.sessionKey = parsed.sessionKey;
          if (parsed.userId) tokens.userId = parsed.userId;
        } catch (e) {
          // Если не JSON, сохраняем как есть
          if (key.includes('access')) tokens.accessToken = value;
          if (key.includes('session')) tokens.sessionKey = value;
        }
      }
    }
  } catch (e) {
    console.log('Ошибка при чтении localStorage:', e);
  }
  
  // 2. Поиск в sessionStorage
  try {
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      const value = sessionStorage.getItem(key);
      
      if (key && (key.includes('sendbird') || key.includes('access') || key.includes('session'))) {
        console.log(`SessionStorage найден: ${key} = ${value}`);
        
        try {
          const parsed = JSON.parse(value);
          if (parsed.accessToken) tokens.accessToken = parsed.accessToken;
          if (parsed.sessionKey) tokens.sessionKey = parsed.sessionKey;
          if (parsed.userId) tokens.userId = parsed.userId;
        } catch (e) {
          if (key.includes('access')) tokens.accessToken = value;
          if (key.includes('session')) tokens.sessionKey = value;
        }
      }
    }
  } catch (e) {
    console.log('Ошибка при чтении sessionStorage:', e);
  }
  
  // 3. Поиск в глобальных объектах window
  try {
    if (window.SendBird || window.sendbird || window.sb) {
      const sb = window.SendBird || window.sendbird || window.sb;
      console.log('Найден объект Sendbird:', sb);
      
      if (sb.currentUser) {
        if (sb.currentUser.userId) tokens.userId = sb.currentUser.userId;
        if (sb.currentUser.accessToken) tokens.accessToken = sb.currentUser.accessToken;
      }
      
      if (typeof sb.getAccessToken === 'function') {
        try {
          const accessToken = sb.getAccessToken();
          if (accessToken) tokens.accessToken = accessToken;
        } catch (e) {
          console.log('Ошибка при вызове getAccessToken:', e);
        }
      }
    }
  } catch (e) {
    console.log('Ошибка при поиске в window объектах:', e);
  }
  
  // 4. Поиск в cookies (маловероятно, но проверим)
  try {
    const cookies = document.cookie.split(';');
    cookies.forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && (name.includes('sendbird') || name.includes('access') || name.includes('session'))) {
        console.log(`Cookie найден: ${name} = ${value}`);
        if (name.includes('access')) tokens.accessToken = value;
        if (name.includes('session')) tokens.sessionKey = value;
      }
    });
  } catch (e) {
    console.log('Ошибка при чтении cookies:', e);
  }
  
  return tokens;
}

// Функция для мониторинга изменений в storage
function monitorStorage() {
  // Слушаем изменения в localStorage
  const originalSetItem = localStorage.setItem;
  localStorage.setItem = function(key, value) {
    if (key && (key.includes('sendbird') || key.includes('access') || key.includes('session'))) {
      console.log(`LocalStorage обновлен: ${key} = ${value}`);
      setTimeout(() => {
        const tokens = extractTokens();
        if (Object.keys(tokens).length > 0) {
          chrome.runtime.sendMessage({
            type: 'TOKENS_FROM_CONTENT',
            tokens: tokens
          });
        }
      }, 100);
    }
    return originalSetItem.apply(this, arguments);
  };
}

// Перехват XMLHttpRequest для поиска токенов в заголовках
function interceptXHR() {
  const originalOpen = XMLHttpRequest.prototype.open;
  const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
  
  XMLHttpRequest.prototype.open = function(method, url) {
    this._url = url;
    this._headers = {};
    return originalOpen.apply(this, arguments);
  };
  
  XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
    this._headers[name] = value;
    
    if (this._url && this._url.includes('sendbird.com')) {
      if (name.toLowerCase() === 'access-token') {
        console.log('XHR: Найден access-token:', value);
        chrome.runtime.sendMessage({
          type: 'TOKENS_FROM_CONTENT',
          tokens: { accessToken: value }
        });
      }
      if (name.toLowerCase() === 'session-key') {
        console.log('XHR: Найден session-key:', value);
        chrome.runtime.sendMessage({
          type: 'TOKENS_FROM_CONTENT',
          tokens: { sessionKey: value }
        });
      }
    }
    
    return originalSetRequestHeader.apply(this, arguments);
  };
}

// Запускаем все методы поиска
setTimeout(() => {
  console.log('G2G Token Extractor: Начинаю поиск токенов...');
  
  const tokens = extractTokens();
  console.log('Найденные токены:', tokens);
  
  if (Object.keys(tokens).length > 0) {
    chrome.runtime.sendMessage({
      type: 'TOKENS_FROM_CONTENT',
      tokens: tokens
    });
  }
  
  // Запускаем мониторинг
  monitorStorage();
  interceptXHR();
  
}, 2000);

// Периодически проверяем токены
setInterval(() => {
  const tokens = extractTokens();
  if (Object.keys(tokens).length > 0) {
    chrome.runtime.sendMessage({
      type: 'TOKENS_FROM_CONTENT',
      tokens: tokens
    });
  }
}, 10000); // каждые 10 секунд
