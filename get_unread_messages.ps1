# Скрипт для получения информации о непрочитанных сообщениях G2G
# Анализ стабильности компонентов:
# - URL домен: СТАБИЛЬНЫЙ (уникальный поддомен Sendbird для G2G)
# - App ID: ОЧЕНЬ СТАБИЛЬНЫЙ (идентификатор приложения)
# - User ID: СТАБИЛЬНЫЙ (ваш ID пользователя)
# - Access Token & Session Key: НЕСТАБИЛЬНЫЕ (могут истекать)

param(
    [string]$UserId = "6484795",
    [string]$AccessToken = "356218d3d04c53341fc99beb84086ce95bfb15fa",
    [string]$SessionKey = "e68bee066a3e3c35e50bbb7a3164b59b4ac48c39",
    [switch]$TestStability,
    [switch]$Verbose
)

# Константы (стабильные компоненты)
$APP_ID = "34201740-152E-401E-AD8F-5C72EEABA386"
$BASE_URL = "https://****************************************.sendbird.com/v3"

# Заголовки запроса
$headers = @{
    'accept' = '*/*'
    'accept-language' = 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7'
    'access-token' = $AccessToken
    'app-id' = $APP_ID
    'content-type' = 'application/json; charset=utf-8'
    'session-key' = $SessionKey
    'sb-user-agent' = 'JS/c4.10.10///oweb'
}

# URL для запроса
$url = "$BASE_URL/users/$UserId/unread_message_count?super_mode=all&include_feed_channel=false"

if ($Verbose) {
    Write-Host "=== АНАЛИЗ СТАБИЛЬНОСТИ КОМПОНЕНТОВ ===" -ForegroundColor Yellow
    Write-Host "App ID: $APP_ID (СТАБИЛЬНЫЙ)" -ForegroundColor Green
    Write-Host "Base URL: $BASE_URL (СТАБИЛЬНЫЙ)" -ForegroundColor Green
    Write-Host "User ID: $UserId (СТАБИЛЬНЫЙ)" -ForegroundColor Green
    Write-Host "Access Token: $($AccessToken.Substring(0,10))... (НЕСТАБИЛЬНЫЙ)" -ForegroundColor Red
    Write-Host "Session Key: $($SessionKey.Substring(0,10))... (НЕСТАБИЛЬНЫЙ)" -ForegroundColor Red
    Write-Host ""
}

Write-Host "Отправка запроса к G2G API..."
Write-Host "URL: $url"
Write-Host ""

# Функция для тестирования стабильности
function Test-ApiStability {
    Write-Host "=== ТЕСТ СТАБИЛЬНОСТИ API ===" -ForegroundColor Cyan

    # Тест 1: Проверка доступности базового URL
    try {
        $testUrl = "$BASE_URL/applications/$APP_ID"
        Invoke-RestMethod -Uri $testUrl -Headers $headers -Method GET -ErrorAction Stop
        Write-Host "✓ Базовый URL стабилен" -ForegroundColor Green
    } catch {
        Write-Host "✗ Базовый URL может быть нестабильным: $($_.Exception.Message)" -ForegroundColor Red
    }

    # Тест 2: Проверка пользователя
    try {
        $userUrl = "$BASE_URL/users/$UserId"
        Invoke-RestMethod -Uri $userUrl -Headers $headers -Method GET -ErrorAction Stop
        Write-Host "✓ User ID стабилен" -ForegroundColor Green
    } catch {
        Write-Host "✗ User ID может быть нестабильным: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host ""
}

if ($TestStability) {
    Test-ApiStability
}

try {
    $response = Invoke-RestMethod -Uri $url -Headers $headers -Method GET

    Write-Host "Успешный ответ:" -ForegroundColor Green
    Write-Host "==============="
    $response | ConvertTo-Json -Depth 10

    # Сохранение результата с временной меткой
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = @{
        timestamp = $timestamp
        unread_count = $response.unread_count
        user_id = $UserId
        success = $true
    }

    if ($Verbose) {
        Write-Host "`nЛог запроса:" -ForegroundColor Yellow
        $logEntry | ConvertTo-Json
    }

} catch {
    Write-Host "Ошибка при выполнении запроса:" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    Write-Host "Error Message: $($_.Exception.Message)"

    # Анализ типичных ошибок
    $statusCode = $_.Exception.Response.StatusCode.value__
    switch ($statusCode) {
        401 { Write-Host "ВЕРОЯТНАЯ ПРИЧИНА: Истек Access Token или Session Key" -ForegroundColor Yellow }
        403 { Write-Host "ВЕРОЯТНАЯ ПРИЧИНА: Недостаточно прав или неверный App ID" -ForegroundColor Yellow }
        404 { Write-Host "ВЕРОЯТНАЯ ПРИЧИНА: Неверный User ID или изменился URL" -ForegroundColor Yellow }
        default { Write-Host "НЕИЗВЕСТНАЯ ОШИБКА: Проверьте все параметры" -ForegroundColor Yellow }
    }

    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
