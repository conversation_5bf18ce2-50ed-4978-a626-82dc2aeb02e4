# Скрипт для получения информации о непрочитанных сообщениях G2G
param(
    [string]$UserId = "6484795",
    [string]$AccessToken = "356218d3d04c53341fc99beb84086ce95bfb15fa",
    [string]$SessionKey = "e68bee066a3e3c35e50bbb7a3164b59b4ac48c39"
)

# Заголовки запроса
$headers = @{
    'accept' = '*/*'
    'accept-language' = 'ru-RU,ru;q=0.9,en-US;q=0.8,en;q=0.7'
    'access-token' = $AccessToken
    'app-id' = '34201740-152E-401E-AD8F-5C72EEABA386'
    'content-type' = 'application/json; charset=utf-8'
    'session-key' = $SessionKey
    'sb-user-agent' = 'JS/c4.10.10///oweb'
}

# URL для запроса
$url = "https://****************************************.sendbird.com/v3/users/$UserId/unread_message_count?super_mode=all&include_feed_channel=false"

Write-Host "Отправка запроса к G2G API..."
Write-Host "URL: $url"
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri $url -Headers $headers -Method GET
    
    Write-Host "Успешный ответ:"
    Write-Host "==============="
    $response | ConvertTo-Json -Depth 10
    
} catch {
    Write-Host "Ошибка при выполнении запроса:"
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
    Write-Host "Status Description: $($_.Exception.Response.StatusDescription)"
    Write-Host "Error Message: $($_.Exception.Message)"
    
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
