# Скрипт для мониторинга валидности токенов G2G
param(
    [string]$AccessToken = "356218d3d04c53341fc99beb84086ce95bfb15fa",
    [string]$SessionKey = "e68bee066a3e3c35e50bbb7a3164b59b4ac48c39",
    [int]$IntervalMinutes = 5
)

$APP_ID = "34201740-152E-401E-AD8F-5C72EEABA386"
$BASE_URL = "https://****************************************.sendbird.com/v3"
$USER_ID = "6484795"

function Test-TokenValidity {
    param($AccessToken, $SessionKey)
    
    $headers = @{
        'accept' = '*/*'
        'access-token' = $AccessToken
        'app-id' = $APP_ID
        'session-key' = $SessionKey
    }
    
    $url = "$BASE_URL/users/$USER_ID/unread_message_count?super_mode=all&include_feed_channel=false"
    
    try {
        $response = Invoke-RestMethod -Uri $url -Headers $headers -Method GET -ErrorAction Stop
        return @{
            valid = $true
            unread_count = $response.unread_count
            error = $null
        }
    } catch {
        return @{
            valid = $false
            unread_count = 0
            error = $_.Exception.Message
            status_code = $_.Exception.Response.StatusCode.value__
        }
    }
}

Write-Host "=== МОНИТОРИНГ ТОКЕНОВ G2G ===" -ForegroundColor Cyan
Write-Host "Интервал проверки: $IntervalMinutes минут"
Write-Host "Нажмите Ctrl+C для остановки"
Write-Host ""

$iteration = 1
while ($true) {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $result = Test-TokenValidity -AccessToken $AccessToken -SessionKey $SessionKey
    
    Write-Host "[$timestamp] Проверка #$iteration" -NoNewline
    
    if ($result.valid) {
        Write-Host " ✓ ТОКЕНЫ ВАЛИДНЫ" -ForegroundColor Green
        Write-Host "  Непрочитанных сообщений: $($result.unread_count)"
    } else {
        Write-Host " ✗ ТОКЕНЫ НЕВАЛИДНЫ" -ForegroundColor Red
        Write-Host "  Ошибка: $($result.error)"
        if ($result.status_code) {
            Write-Host "  Код ошибки: $($result.status_code)"
        }
        
        # Рекомендации по исправлению
        switch ($result.status_code) {
            401 { Write-Host "  РЕКОМЕНДАЦИЯ: Обновите Access Token и Session Key" -ForegroundColor Yellow }
            403 { Write-Host "  РЕКОМЕНДАЦИЯ: Проверьте права доступа" -ForegroundColor Yellow }
            404 { Write-Host "  РЕКОМЕНДАЦИЯ: Проверьте User ID" -ForegroundColor Yellow }
        }
    }
    
    Write-Host ""
    $iteration++
    Start-Sleep -Seconds ($IntervalMinutes * 60)
}
